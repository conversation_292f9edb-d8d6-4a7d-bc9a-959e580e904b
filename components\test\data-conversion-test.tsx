"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ServiceStorage } from "@/lib/service-storage"
import { StaffDataService } from "@/lib/staff-data-service"
import { ClientDataService } from "@/lib/client-data-service"
import { SettingsStorage } from "@/lib/settings-storage"
import { getMigrationStatus, isMigrationNeeded } from "@/lib/data-migration-service"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { CheckCircle, XCircle, RefreshCw, Database } from "lucide-react"

export function DataConversionTest() {
  const [testResults, setTestResults] = useState<any>({})
  const [isLoading, setIsLoading] = useState(false)
  const { currencyCode, setCurrency } = useCurrency()

  const runTests = async () => {
    setIsLoading(true)
    const results: any = {}

    try {
      // Test Services
      console.log("Testing services...")
      const services = ServiceStorage.getServices()
      const categories = ServiceStorage.getServiceCategories()
      results.services = {
        count: services.length,
        categoriesCount: categories.length,
        hasData: services.length > 0,
        sampleService: services[0]?.name || "None"
      }

      // Test Staff
      console.log("Testing staff...")
      const staff = StaffDataService.getStaff()
      results.staff = {
        count: staff.length,
        hasData: staff.length > 0,
        activeCount: staff.filter(s => s.status === "Active").length,
        sampleStaff: staff[0]?.name || "None"
      }

      // Test Clients
      console.log("Testing clients...")
      const clients = ClientDataService.getClients()
      results.clients = {
        count: clients.length,
        hasData: clients.length > 0,
        vipCount: clients.filter(c => c.segment === "VIP").length,
        sampleClient: clients[0]?.name || "None"
      }

      // Test Locations
      console.log("Testing locations...")
      const locations = SettingsStorage.getLocations()
      results.locations = {
        count: locations.length,
        hasData: locations.length > 0,
        activeCount: locations.filter(l => l.status === "Active").length,
        sampleLocation: locations[0]?.name || "None"
      }

      // Test Migration Status
      console.log("Testing migration status...")
      const migrationStatus = getMigrationStatus()
      const migrationNeeded = isMigrationNeeded()
      results.migration = {
        isComplete: !migrationNeeded,
        status: migrationStatus,
        completedMigrations: Object.keys(migrationStatus).filter(key => migrationStatus[key]).length
      }

      // Test Data Persistence
      console.log("Testing data persistence...")
      const testKey = 'test_persistence_' + Date.now()
      const testData = { test: true, timestamp: Date.now() }
      localStorage.setItem(testKey, JSON.stringify(testData))
      const retrieved = JSON.parse(localStorage.getItem(testKey) || '{}')
      localStorage.removeItem(testKey)
      results.persistence = {
        canWrite: true,
        canRead: retrieved.test === true,
        working: retrieved.test === true
      }

      // Test Currency
      console.log("Testing currency...")
      results.currency = {
        currentCode: currencyCode,
        hasProvider: !!currencyCode,
        working: !!currencyCode
      }

      setTestResults(results)
      console.log("All tests completed:", results)

    } catch (error) {
      console.error("Error running tests:", error)
      results.error = error.message
      setTestResults(results)
    }

    setIsLoading(false)
  }

  useEffect(() => {
    // Run tests on component mount
    runTests()
  }, [])

  const testCurrencyChange = () => {
    const newCurrency = currencyCode === 'QAR' ? 'USD' : 'QAR'
    setCurrency(newCurrency)
  }

  const getStatusIcon = (isWorking: boolean) => {
    return isWorking ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    )
  }

  const getStatusBadge = (isWorking: boolean) => {
    return (
      <Badge variant={isWorking ? "default" : "destructive"}>
        {isWorking ? "Working" : "Failed"}
      </Badge>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Conversion Test</h1>
          <p className="text-gray-600 mt-2">
            Verify that all mock data has been successfully converted to persistent real data.
          </p>
        </div>
        <Button onClick={runTests} disabled={isLoading}>
          {isLoading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <Database className="h-4 w-4 mr-2" />
              Run Tests
            </>
          )}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Services Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Services Data</CardTitle>
            {testResults.services && getStatusIcon(testResults.services.hasData)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.services?.count || 0} Services
            </div>
            <p className="text-xs text-muted-foreground">
              {testResults.services?.categoriesCount || 0} categories
            </p>
            <div className="mt-2">
              {testResults.services && getStatusBadge(testResults.services.hasData)}
            </div>
            {testResults.services?.sampleService && (
              <p className="text-xs mt-2">Sample: {testResults.services.sampleService}</p>
            )}
          </CardContent>
        </Card>

        {/* Staff Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff Data</CardTitle>
            {testResults.staff && getStatusIcon(testResults.staff.hasData)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.staff?.count || 0} Staff
            </div>
            <p className="text-xs text-muted-foreground">
              {testResults.staff?.activeCount || 0} active
            </p>
            <div className="mt-2">
              {testResults.staff && getStatusBadge(testResults.staff.hasData)}
            </div>
            {testResults.staff?.sampleStaff && (
              <p className="text-xs mt-2">Sample: {testResults.staff.sampleStaff}</p>
            )}
          </CardContent>
        </Card>

        {/* Clients Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clients Data</CardTitle>
            {testResults.clients && getStatusIcon(testResults.clients.hasData)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.clients?.count || 0} Clients
            </div>
            <p className="text-xs text-muted-foreground">
              {testResults.clients?.vipCount || 0} VIP clients
            </p>
            <div className="mt-2">
              {testResults.clients && getStatusBadge(testResults.clients.hasData)}
            </div>
            {testResults.clients?.sampleClient && (
              <p className="text-xs mt-2">Sample: {testResults.clients.sampleClient}</p>
            )}
          </CardContent>
        </Card>

        {/* Locations Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Locations Data</CardTitle>
            {testResults.locations && getStatusIcon(testResults.locations.hasData)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.locations?.count || 0} Locations
            </div>
            <p className="text-xs text-muted-foreground">
              {testResults.locations?.activeCount || 0} active
            </p>
            <div className="mt-2">
              {testResults.locations && getStatusBadge(testResults.locations.hasData)}
            </div>
            {testResults.locations?.sampleLocation && (
              <p className="text-xs mt-2">Sample: {testResults.locations.sampleLocation}</p>
            )}
          </CardContent>
        </Card>

        {/* Migration Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Migration Status</CardTitle>
            {testResults.migration && getStatusIcon(testResults.migration.isComplete)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.migration?.isComplete ? "Complete" : "Pending"}
            </div>
            <p className="text-xs text-muted-foreground">
              {testResults.migration?.completedMigrations || 0} migrations done
            </p>
            <div className="mt-2">
              {testResults.migration && getStatusBadge(testResults.migration.isComplete)}
            </div>
          </CardContent>
        </Card>

        {/* Currency Test */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Currency Integration</CardTitle>
            {testResults.currency && getStatusIcon(testResults.currency.working)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {testResults.currency?.currentCode || "N/A"}
            </div>
            <p className="text-xs text-muted-foreground">
              <CurrencyDisplay amount={100} className="text-xs" />
            </p>
            <div className="mt-2 space-y-2">
              {testResults.currency && getStatusBadge(testResults.currency.working)}
              <Button size="sm" variant="outline" onClick={testCurrencyChange}>
                Test Currency Change
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Test Results</CardTitle>
            <CardDescription>
              Raw test data for debugging purposes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
