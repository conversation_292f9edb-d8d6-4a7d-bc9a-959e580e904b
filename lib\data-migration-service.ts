"use client"

import { ServiceStorage } from "@/lib/service-storage"
import { StaffDataService } from "@/lib/staff-data-service"
import { ClientDataService } from "@/lib/client-data-service"
import { SettingsStorage } from "@/lib/settings-storage"
import { initializeAllData } from "@/lib/data-initialization"

/**
 * Data Migration Service
 * Handles the conversion from mock data to persistent real data
 * Ensures smooth transition while maintaining data integrity
 */
export class DataMigrationService {
  private static instance: DataMigrationService
  private migrationStatus: { [key: string]: boolean } = {}
  
  private constructor() {
    this.loadMigrationStatus()
  }
  
  static getInstance(): DataMigrationService {
    if (!DataMigrationService.instance) {
      DataMigrationService.instance = new DataMigrationService()
    }
    return DataMigrationService.instance
  }
  
  /**
   * Load migration status from localStorage
   */
  private loadMigrationStatus() {
    try {
      const stored = localStorage.getItem('vanity_migration_status')
      if (stored) {
        this.migrationStatus = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Error loading migration status:', error)
      this.migrationStatus = {}
    }
  }
  
  /**
   * Save migration status to localStorage
   */
  private saveMigrationStatus() {
    try {
      localStorage.setItem('vanity_migration_status', JSON.stringify(this.migrationStatus))
    } catch (error) {
      console.error('Error saving migration status:', error)
    }
  }
  
  /**
   * Mark a migration as completed
   */
  private markMigrationComplete(migrationKey: string) {
    this.migrationStatus[migrationKey] = true
    this.saveMigrationStatus()
    console.log(`Migration completed: ${migrationKey}`)
  }
  
  /**
   * Check if a migration has been completed
   */
  private isMigrationComplete(migrationKey: string): boolean {
    return this.migrationStatus[migrationKey] === true
  }
  
  /**
   * Perform complete data migration
   */
  async performCompleteMigration(): Promise<void> {
    console.log("=== STARTING COMPLETE DATA MIGRATION ===")
    
    try {
      // Migrate services
      await this.migrateServices()
      
      // Migrate staff
      await this.migrateStaff()
      
      // Migrate clients
      await this.migrateClients()
      
      // Migrate locations (already handled by SettingsStorage)
      await this.migrateLocations()
      
      // Initialize remaining data
      await this.initializeRemainingData()
      
      // Verify data integrity
      await this.verifyDataIntegrity()
      
      console.log("=== COMPLETE DATA MIGRATION FINISHED ===")
      
    } catch (error) {
      console.error("Error during data migration:", error)
      throw error
    }
  }
  
  /**
   * Migrate services from mock to persistent data
   */
  private async migrateServices(): Promise<void> {
    const migrationKey = 'services_v1'
    
    if (this.isMigrationComplete(migrationKey)) {
      console.log("Services migration already completed")
      return
    }
    
    console.log("Migrating services...")
    
    try {
      // Initialize services using the new service storage
      ServiceStorage.initializeServices()
      ServiceStorage.initializeServiceCategories()
      
      this.markMigrationComplete(migrationKey)
      console.log("Services migration completed successfully")
      
    } catch (error) {
      console.error("Error migrating services:", error)
      throw error
    }
  }
  
  /**
   * Migrate staff from mock to persistent data
   */
  private async migrateStaff(): Promise<void> {
    const migrationKey = 'staff_v1'
    
    if (this.isMigrationComplete(migrationKey)) {
      console.log("Staff migration already completed")
      return
    }
    
    console.log("Migrating staff...")
    
    try {
      // Initialize staff using the new staff data service
      StaffDataService.initializeStaff()
      
      this.markMigrationComplete(migrationKey)
      console.log("Staff migration completed successfully")
      
    } catch (error) {
      console.error("Error migrating staff:", error)
      throw error
    }
  }
  
  /**
   * Migrate clients from mock to persistent data
   */
  private async migrateClients(): Promise<void> {
    const migrationKey = 'clients_v1'
    
    if (this.isMigrationComplete(migrationKey)) {
      console.log("Clients migration already completed")
      return
    }
    
    console.log("Migrating clients...")
    
    try {
      // Initialize clients using the new client data service
      ClientDataService.initializeClients()
      
      this.markMigrationComplete(migrationKey)
      console.log("Clients migration completed successfully")
      
    } catch (error) {
      console.error("Error migrating clients:", error)
      throw error
    }
  }
  
  /**
   * Migrate locations (already handled by SettingsStorage)
   */
  private async migrateLocations(): Promise<void> {
    const migrationKey = 'locations_v1'
    
    if (this.isMigrationComplete(migrationKey)) {
      console.log("Locations migration already completed")
      return
    }
    
    console.log("Migrating locations...")
    
    try {
      // Locations are automatically initialized by SettingsStorage
      SettingsStorage.getLocations()
      
      this.markMigrationComplete(migrationKey)
      console.log("Locations migration completed successfully")
      
    } catch (error) {
      console.error("Error migrating locations:", error)
      throw error
    }
  }
  
  /**
   * Initialize remaining data (appointments, transactions, etc.)
   */
  private async initializeRemainingData(): Promise<void> {
    const migrationKey = 'remaining_data_v1'
    
    if (this.isMigrationComplete(migrationKey)) {
      console.log("Remaining data initialization already completed")
      return
    }
    
    console.log("Initializing remaining data...")
    
    try {
      // Use the existing data initialization service
      initializeAllData()
      
      this.markMigrationComplete(migrationKey)
      console.log("Remaining data initialization completed successfully")
      
    } catch (error) {
      console.error("Error initializing remaining data:", error)
      throw error
    }
  }
  
  /**
   * Verify data integrity after migration
   */
  private async verifyDataIntegrity(): Promise<void> {
    console.log("Verifying data integrity...")
    
    try {
      // Check services
      const services = ServiceStorage.getServices()
      const categories = ServiceStorage.getServiceCategories()
      console.log(`Verified: ${services.length} services, ${categories.length} categories`)
      
      // Check staff
      const staff = StaffDataService.getStaff()
      console.log(`Verified: ${staff.length} staff members`)
      
      // Check clients
      const clients = ClientDataService.getClients()
      console.log(`Verified: ${clients.length} clients`)
      
      // Check locations
      const locations = SettingsStorage.getLocations()
      console.log(`Verified: ${locations.length} locations`)
      
      console.log("Data integrity verification completed successfully")
      
    } catch (error) {
      console.error("Error during data integrity verification:", error)
      throw error
    }
  }
  
  /**
   * Get migration status report
   */
  getMigrationStatus(): { [key: string]: boolean } {
    return { ...this.migrationStatus }
  }
  
  /**
   * Reset migration status (for testing purposes)
   */
  resetMigrationStatus(): void {
    this.migrationStatus = {}
    this.saveMigrationStatus()
    console.log("Migration status reset")
  }
  
  /**
   * Check if all migrations are complete
   */
  isFullyMigrated(): boolean {
    const requiredMigrations = [
      'services_v1',
      'staff_v1', 
      'clients_v1',
      'locations_v1',
      'remaining_data_v1'
    ]
    
    return requiredMigrations.every(migration => this.isMigrationComplete(migration))
  }
}

/**
 * Convenience function to perform migration
 */
export async function performDataMigration(): Promise<void> {
  const migrationService = DataMigrationService.getInstance()
  await migrationService.performCompleteMigration()
}

/**
 * Check if migration is needed
 */
export function isMigrationNeeded(): boolean {
  const migrationService = DataMigrationService.getInstance()
  return !migrationService.isFullyMigrated()
}

/**
 * Get migration status
 */
export function getMigrationStatus(): { [key: string]: boolean } {
  const migrationService = DataMigrationService.getInstance()
  return migrationService.getMigrationStatus()
}
