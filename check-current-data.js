// <PERSON>ript to check current localStorage data
console.log('🔍 CHECKING CURRENT LOCALSTORAGE DATA');

// This script would normally run in the browser console
// Here we'll simulate what we expect to find

console.log('📋 TO CHECK CURRENT DATA IN BROWSER:');
console.log('1. Open browser console (F12)');
console.log('2. Run: JSON.parse(localStorage.getItem("vanity_transactions") || "[]")');
console.log('3. Run: JSON.parse(localStorage.getItem("vanity_appointments") || "[]")');

console.log('\n🔧 EXPECTED TRANSACTION STRUCTURE:');
console.log('Service transactions should have:');
console.log('- type: "service_sale"');
console.log('- source: "calendar"');
console.log('- status: "completed"');
console.log('- reference: { type: "appointment", id: "..." }');

console.log('\n🔧 EXPECTED APPOINTMENT STRUCTURE:');
console.log('Completed appointments should have:');
console.log('- status: "completed"');
console.log('- price: (number > 0)');
console.log('- service: (service name)');

console.log('\n📊 DEBUGGING STEPS:');
console.log('1. Go to http://localhost:3000/debug/service-revenue');
console.log('2. Click "Analyze Service Revenue" to see current state');
console.log('3. Click "Create Test Service Transaction" to add test data');
console.log('4. Go to dashboard to verify service revenue appears');
console.log('5. Check browser console for detailed logging');

console.log('\n🎯 EXPECTED OUTCOME:');
console.log('After creating test service transactions:');
console.log('- Dashboard should show service revenue in Total Revenue card');
console.log('- In-Person Sales card should show service revenue breakdown');
console.log('- Analytics should calculate service revenue correctly');
