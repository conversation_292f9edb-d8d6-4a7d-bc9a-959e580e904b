"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"

// Product interface for database-driven products
export interface Product {
  id: string
  name: string
  description?: string
  price: number
  salePrice?: number
  cost?: number
  category: string
  type?: string
  image?: string
  images?: string[]
  stock: number
  minStock?: number
  isNew?: boolean
  isBestSeller?: boolean
  isSale?: boolean
  isOnSale?: boolean
  features?: string[]
  ingredients?: string[]
  howToUse?: string[]
  relatedProducts?: string[]
  rating?: number
  reviewCount?: number
  sku?: string
  barcode?: string
  location?: string
  isRetail?: boolean
  isActive?: boolean
  isFeatured?: boolean
  createdAt?: Date
  updatedAt?: Date
}

// Simplified context interface for database-driven products
interface ProductContextType {
  products: Product[]
  getProductById: (id: string) => Product | undefined
  getProductsByCategory: (category: string) => Product[]
  getRetailProducts: () => Product[]
  refreshProducts: () => Promise<void>
  isLoading: boolean
  error: string | null
}

// Create context with default values
const ProductContext = createContext<ProductContextType>({
  products: [],
  getProductById: () => undefined,
  getProductsByCategory: () => [],
  getRetailProducts: () => [],
  refreshProducts: async () => {},
  isLoading: false,
  error: null,
})

export function ProductProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch products from database API
  const fetchProducts = useCallback(async (): Promise<void> => {
    setIsLoading(true)
    setError(null)
    
    try {
      console.log('🔄 Fetching products from database API...')
      const response = await fetch('/api/products?isRetail=true')

      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.statusText}`)
      }

      const data = await response.json()
      console.log(`✅ Fetched ${data.products?.length || 0} products from database`)

      setProducts(data.products || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products'
      console.error('❌ Error fetching products:', err)
      setError(errorMessage)
      setProducts([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Initialize products on mount
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  // Helper functions
  const getProductById = useCallback((id: string) => {
    return products.find(product => product.id === id)
  }, [products])

  const getProductsByCategory = useCallback((category: string) => {
    return products.filter(product => product.category === category)
  }, [products])

  const getRetailProducts = useCallback(() => {
    return products.filter(product => product.isRetail && product.isActive)
  }, [products])

  return (
    <ProductContext.Provider
      value={{
        products,
        getProductById,
        getProductsByCategory,
        getRetailProducts,
        refreshProducts: fetchProducts,
        isLoading,
        error,
      }}
    >
      {children}
    </ProductContext.Provider>
  )
}

export const useProducts = () => {
  const context = useContext(ProductContext)
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider')
  }
  return context
}
