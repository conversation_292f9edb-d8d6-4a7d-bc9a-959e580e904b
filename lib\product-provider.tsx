"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/components/ui/use-toast"

// Product interface for database-driven products
export interface Product {
  id: string
  name: string
  description?: string
  price: number
  salePrice?: number
  cost?: number
  category: string
  type?: string
  image?: string
  images?: string[]
  stock: number
  minStock?: number
  isNew?: boolean
  isBestSeller?: boolean
  isSale?: boolean
  isOnSale?: boolean
  features?: string[]
  ingredients?: string[]
  howToUse?: string[]
  relatedProducts?: string[]
  rating?: number
  reviewCount?: number
  sku?: string
  barcode?: string
  location?: string
  isRetail?: boolean
  isActive?: boolean
  isFeatured?: boolean
  createdAt?: Date
  updatedAt?: Date
}

// Product Category interface
export interface ProductCategory {
  id: string
  name: string
  description?: string
  isActive: boolean
  productCount: number
  createdAt: Date
  updatedAt: Date
}

// Product Type interface
export interface ProductType {
  id: string
  name: string
  description?: string
  categoryId?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Context interface with full functionality
interface ProductContextType {
  // Products
  products: Product[]
  getProductById: (id: string) => Product | undefined
  getProductsByCategory: (category: string) => Product[]
  getRetailProducts: () => Product[]
  addProduct: (productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => Product
  updateProduct: (updatedProduct: Product) => void
  deleteProduct: (productId: string) => boolean
  refreshProducts: () => Promise<void>
  isLoading: boolean
  error: string | null

  // Categories
  categories: ProductCategory[]
  getCategoryById: (id: string) => ProductCategory | undefined
  getCategoryName: (id: string) => string
  addCategory: (categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => ProductCategory
  updateCategory: (updatedCategory: ProductCategory) => void
  deleteCategory: (categoryId: string) => boolean
  refreshCategories: () => void

  // Product Types
  productTypes: ProductType[]
  getProductTypeById: (id: string) => ProductType | undefined
  getProductTypeName: (id: string) => string
  getProductTypesByCategory: (categoryId: string) => ProductType[]
  addProductType: (typeData: Omit<ProductType, "id" | "createdAt" | "updatedAt">) => ProductType
  updateProductType: (updatedType: ProductType) => void
  deleteProductType: (typeId: string) => boolean
  refreshProductTypes: () => void

  // Shop Integration
  ensureShopIntegration: (triggerUpdate?: boolean) => Product[]
  refreshShop: () => void
}

// Default categories
const defaultCategories: ProductCategory[] = [
  { id: "1", name: "Skincare", description: "Skincare products", isActive: true, productCount: 0, createdAt: new Date(), updatedAt: new Date() },
  { id: "2", name: "Makeup", description: "Makeup products", isActive: true, productCount: 0, createdAt: new Date(), updatedAt: new Date() },
  { id: "3", name: "Hair Care", description: "Hair care products", isActive: true, productCount: 0, createdAt: new Date(), updatedAt: new Date() },
  { id: "4", name: "Nail Care", description: "Nail care products", isActive: true, productCount: 0, createdAt: new Date(), updatedAt: new Date() },
  { id: "5", name: "Tools", description: "Beauty tools", isActive: true, productCount: 0, createdAt: new Date(), updatedAt: new Date() },
]

// Default product types
const defaultProductTypes: ProductType[] = [
  { id: "1", name: "Cleanser", categoryId: "1", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "2", name: "Moisturizer", categoryId: "1", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "3", name: "Foundation", categoryId: "2", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "4", name: "Lipstick", categoryId: "2", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "5", name: "Shampoo", categoryId: "3", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "6", name: "Conditioner", categoryId: "3", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "7", name: "Nail Polish", categoryId: "4", isActive: true, createdAt: new Date(), updatedAt: new Date() },
  { id: "8", name: "Brush", categoryId: "5", isActive: true, createdAt: new Date(), updatedAt: new Date() },
]

// Create context with default values
const ProductContext = createContext<ProductContextType>({
  products: [],
  getProductById: () => undefined,
  getProductsByCategory: () => [],
  getRetailProducts: () => [],
  addProduct: () => ({} as Product),
  updateProduct: () => {},
  deleteProduct: () => false,
  refreshProducts: async () => {},
  isLoading: false,
  error: null,

  categories: [],
  getCategoryById: () => undefined,
  getCategoryName: () => "Uncategorized",
  addCategory: () => ({} as ProductCategory),
  updateCategory: () => {},
  deleteCategory: () => false,
  refreshCategories: () => {},

  productTypes: [],
  getProductTypeById: () => undefined,
  getProductTypeName: () => "Other",
  getProductTypesByCategory: () => [],
  addProductType: () => ({} as ProductType),
  updateProductType: () => {},
  deleteProductType: () => false,
  refreshProductTypes: () => {},

  ensureShopIntegration: () => [],
  refreshShop: () => {},
})

export function ProductProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<ProductCategory[]>(defaultCategories)
  const [productTypes, setProductTypes] = useState<ProductType[]>(defaultProductTypes)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch products from database API
  const fetchProducts = useCallback(async (): Promise<void> => {
    setIsLoading(true)
    setError(null)

    try {
      console.log('🔄 Fetching products from database API...')
      const response = await fetch('/api/products?isRetail=true')

      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.statusText}`)
      }

      const data = await response.json()
      console.log(`✅ Fetched ${data.products?.length || 0} products from database`)

      setProducts(data.products || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products'
      console.error('❌ Error fetching products:', err)
      setError(errorMessage)
      setProducts([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Initialize products on mount
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  // Product methods
  const getProductById = useCallback((id: string) => {
    return products.find(product => product.id === id)
  }, [products])

  const getProductsByCategory = useCallback((category: string) => {
    return products.filter(product => product.category === category)
  }, [products])

  const getRetailProducts = useCallback(() => {
    return products.filter(product => product.isRetail && product.isActive)
  }, [products])

  const addProduct = useCallback((productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => {
    const newProduct: Product = {
      id: uuidv4(),
      ...productData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setProducts(prev => [...prev, newProduct])

    toast({
      title: "Product added",
      description: `${newProduct.name} has been added to inventory.`,
    })

    return newProduct
  }, [toast])

  const updateProduct = useCallback((updatedProduct: Product) => {
    setProducts(prev => prev.map(product =>
      product.id === updatedProduct.id
        ? { ...updatedProduct, updatedAt: new Date() }
        : product
    ))

    toast({
      title: "Product updated",
      description: `${updatedProduct.name} has been updated.`,
    })
  }, [toast])

  const deleteProduct = useCallback((productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return false

    setProducts(prev => prev.filter(p => p.id !== productId))

    toast({
      title: "Product deleted",
      description: `${product.name} has been removed from inventory.`,
    })

    return true
  }, [products, toast])

  // Category methods
  const getCategoryById = useCallback((id: string) => {
    return categories.find(category => category.id === id)
  }, [categories])

  const getCategoryName = useCallback((id: string) => {
    const category = categories.find(c => c.id === id || c.name === id)
    return category?.name || "Uncategorized"
  }, [categories])

  const addCategory = useCallback((categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => {
    const newCategory: ProductCategory = {
      id: uuidv4(),
      ...categoryData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setCategories(prev => [...prev, newCategory])

    toast({
      title: "Category added",
      description: `${newCategory.name} category has been created.`,
    })

    return newCategory
  }, [toast])

  const updateCategory = useCallback((updatedCategory: ProductCategory) => {
    setCategories(prev => prev.map(category =>
      category.id === updatedCategory.id
        ? { ...updatedCategory, updatedAt: new Date() }
        : category
    ))

    toast({
      title: "Category updated",
      description: `${updatedCategory.name} category has been updated.`,
    })
  }, [toast])

  const deleteCategory = useCallback((categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (!category) return false

    setCategories(prev => prev.filter(c => c.id !== categoryId))

    toast({
      title: "Category deleted",
      description: `${category.name} category has been removed.`,
    })

    return true
  }, [categories, toast])

  const refreshCategories = useCallback(() => {
    // For now, just keep the default categories
    setCategories(defaultCategories)
  }, [])

  // Product Type methods
  const getProductTypeById = useCallback((id: string) => {
    return productTypes.find(type => type.id === id)
  }, [productTypes])

  const getProductTypeName = useCallback((id: string) => {
    const type = productTypes.find(t => t.id === id || t.name === id)
    return type?.name || "Other"
  }, [productTypes])

  const getProductTypesByCategory = useCallback((categoryId: string) => {
    return productTypes.filter(type => type.categoryId === categoryId && type.isActive)
  }, [productTypes])

  const addProductType = useCallback((typeData: Omit<ProductType, "id" | "createdAt" | "updatedAt">) => {
    const newType: ProductType = {
      id: uuidv4(),
      ...typeData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setProductTypes(prev => [...prev, newType])

    toast({
      title: "Product type added",
      description: `${newType.name} type has been created.`,
    })

    return newType
  }, [toast])

  const updateProductType = useCallback((updatedType: ProductType) => {
    setProductTypes(prev => prev.map(type =>
      type.id === updatedType.id
        ? { ...updatedType, updatedAt: new Date() }
        : type
    ))

    toast({
      title: "Product type updated",
      description: `${updatedType.name} type has been updated.`,
    })
  }, [toast])

  const deleteProductType = useCallback((typeId: string) => {
    const type = productTypes.find(t => t.id === typeId)
    if (!type) return false

    setProductTypes(prev => prev.filter(t => t.id !== typeId))

    toast({
      title: "Product type deleted",
      description: `${type.name} type has been removed.`,
    })

    return true
  }, [productTypes, toast])

  const refreshProductTypes = useCallback(() => {
    // For now, just keep the default types
    setProductTypes(defaultProductTypes)
  }, [])

  // Shop integration methods
  const ensureShopIntegration = useCallback((triggerUpdate = true) => {
    console.log("🔄 Ensuring shop integration...")
    const retailProducts = getRetailProducts()
    console.log("🛒 Current retail products for shop:", retailProducts.length)
    return retailProducts
  }, [getRetailProducts])

  const refreshShop = useCallback(() => {
    console.log("🔄 Refreshing shop...")
    fetchProducts()
  }, [fetchProducts])

  return (
    <ProductContext.Provider
      value={{
        products,
        getProductById,
        getProductsByCategory,
        getRetailProducts,
        addProduct,
        updateProduct,
        deleteProduct,
        refreshProducts: fetchProducts,
        isLoading,
        error,

        categories,
        getCategoryById,
        getCategoryName,
        addCategory,
        updateCategory,
        deleteCategory,
        refreshCategories,

        productTypes,
        getProductTypeById,
        getProductTypeName,
        getProductTypesByCategory,
        addProductType,
        updateProductType,
        deleteProductType,
        refreshProductTypes,

        ensureShopIntegration,
        refreshShop,
      }}
    >
      {children}
    </ProductContext.Provider>
  )
}

export const useProducts = () => {
  const context = useContext(ProductContext)
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider')
  }
  return context
}
