// Utility to reset all data to defaults
import { beautyProducts } from './products-data'

export function resetAllData() {
  if (typeof window === 'undefined') return

  try {
    // Clear all salon-related localStorage
    const keysToRemove = [
      'salon_products',
      'salon_product_categories', 
      'salon_product_types',
      'vanity_staff',
      'vanity_clients',
      'vanity_services',
      'vanity_appointments',
      'vanity_schedule',
      'vanity_locations',
      'vanity_settings'
    ]

    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })

    console.log('✅ All data cleared from localStorage')

    // Force reload to reinitialize with mock data
    window.location.reload()
  } catch (error) {
    console.error('❌ Error resetting data:', error)
  }
}

export function resetStaffData() {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem('vanity_staff')
    console.log('✅ Staff data cleared from localStorage')

    // Note: Staff data will be reinitialized automatically by the staff provider
    // when it detects no data in localStorage. The FileStaffStorage system
    // will handle providing the default 7 real staff members.

    // Dispatch event to trigger staff provider refresh
    window.dispatchEvent(new CustomEvent('staff-updated'))

    console.log('✅ Staff data reset complete - will reload with 7 real staff members')

    // Force page reload to reinitialize staff data
    window.location.reload()
  } catch (error) {
    console.error('❌ Error resetting staff data:', error)
  }
}

export function resetProductData() {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem('salon_products')
    localStorage.removeItem('salon_product_categories')
    localStorage.removeItem('salon_product_types')
    console.log('✅ Product data cleared, will reload with mock data')
    
    // Force page reload to reinitialize products
    window.location.reload()
  } catch (error) {
    console.error('❌ Error resetting product data:', error)
  }
}

export function getDataStatus() {
  if (typeof window === 'undefined') return null

  const staffData = localStorage.getItem('vanity_staff')
  const productData = localStorage.getItem('salon_products')
  
  return {
    staff: {
      exists: !!staffData,
      count: staffData ? JSON.parse(staffData).length : 0
    },
    products: {
      exists: !!productData,
      count: productData ? JSON.parse(productData).length : 0
    }
  }
}

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).resetAllData = resetAllData
  (window as any).resetStaffData = resetStaffData
  (window as any).resetProductData = resetProductData
  (window as any).getDataStatus = getDataStatus
}
