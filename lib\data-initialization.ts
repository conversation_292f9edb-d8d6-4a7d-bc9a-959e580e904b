"use client"

import { ServiceStorage } from "@/lib/service-storage"
import { StaffDataService } from "@/lib/staff-data-service"
import { ClientDataService } from "@/lib/client-data-service"
import { SettingsStorage } from "@/lib/settings-storage"
import { TransactionType, TransactionSource, TransactionStatus, PaymentMethod } from './transaction-types'
// Temporary import to resolve cache issue - will be removed
import { mockTransactions, mockExpenses } from '@/lib/mock-data'

// Initialize all data using the new data services
export function initializeAllData() {
  console.log("=== INITIALIZING ALL PERSISTENT DATA ===")

  // Initialize services using ServiceStorage
  console.log("Initializing services...")
  ServiceStorage.initializeServices()
  ServiceStorage.initializeServiceCategories()

  // Initialize staff using StaffDataService
  console.log("Initializing staff...")
  StaffDataService.initializeStaff()

  // Initialize clients using ClientDataService
  console.log("Initializing clients...")
  ClientDataService.initializeClients()

  // Initialize locations (already handled by SettingsStorage)
  console.log("Initializing locations...")
  SettingsStorage.getLocations() // This will auto-initialize if needed

  // Initialize appointments (this will also create transactions for completed ones)
  initializeAppointments()

  // Initialize base transactions (from existing data)
  initializeBaseTransactions()

  // Initialize expenses
  initializeExpenses()

  console.log("=== PERSISTENT DATA INITIALIZATION COMPLETE ===")
}

// Default appointments data for initial setup
const defaultAppointments = [
  {
    id: "a1",
    clientId: "ed1",
    clientName: "Emily Davis",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Haircut & Style",
    serviceId: "1",
    date: new Date().toISOString(), // Today
    duration: 60,
    status: "confirmed",
    location: "loc1",
    price: 75,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    statusHistory: [
      {
        status: "pending",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a2",
    clientId: "jw2",
    clientName: "James Wilson",
    staffId: "2",
    staffName: "Michael Chen",
    service: "Full Color",
    serviceId: "2",
    date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
    duration: 120,
    status: "arrived",
    location: "loc1",
    price: 120,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    statusHistory: [
      {
        status: "pending",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedBy: "Staff"
      },
      {
        status: "arrived",
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a3",
    clientId: "sw3",
    clientName: "Sarah Williams",
    staffId: "3",
    staffName: "Sophia Rodriguez",
    service: "Manicure",
    serviceId: "3",
    date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    duration: 45,
    status: "completed",
    location: "loc2",
    price: 35,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    statusHistory: [
      {
        status: "pending",
        timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(), // 2 days ago
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        updatedBy: "Staff"
      },
      {
        status: "completed",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedBy: "Staff"
      }
    ]
  },
  {
    id: "a4",
    clientId: "test-client-4",
    clientName: "Test Client 4",
    staffId: "1",
    staffName: "Emma Johnson",
    service: "Beard Trim",
    serviceId: "3",
    date: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
    duration: 30,
    status: "service-started",
    location: "loc1",
    price: 35,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    statusHistory: [
      {
        status: "pending",
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        updatedBy: "Client"
      },
      {
        status: "confirmed",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        updatedBy: "Staff"
      },
      {
        status: "arrived",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        updatedBy: "Staff"
      },
      {
        status: "service-started",
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        updatedBy: "Staff"
      }
    ]
  }
]

// Initialize appointments and create transactions for completed ones
function initializeAppointments() {
  const existingAppointments = localStorage.getItem('vanity_appointments')
  if (!existingAppointments) {
    console.log("Initializing appointments data...")
    localStorage.setItem('vanity_appointments', JSON.stringify(defaultAppointments))
    console.log(`Initialized ${defaultAppointments.length} appointments`)

    // Create transactions for completed appointments
    createTransactionsForCompletedAppointments()
  } else {
    console.log("Appointments data already exists")
    // Still check for missing transactions
    createTransactionsForCompletedAppointments()
  }
}

// Create transactions for completed appointments
function createTransactionsForCompletedAppointments() {
  console.log("Creating transactions for completed appointments...")

  const appointmentsData = localStorage.getItem('vanity_appointments')
  if (!appointmentsData) return

  const appointments = JSON.parse(appointmentsData)
  const completedAppointments = appointments.filter((apt: any) => apt.status === 'completed')

  console.log(`Found ${completedAppointments.length} completed appointments`)

  // Get existing transactions to avoid duplicates
  const existingTransactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]')

  let createdCount = 0

  completedAppointments.forEach((appointment: any) => {
    // Check if transaction already exists
    const existingTransaction = existingTransactions.find((tx: any) =>
      tx.reference?.type === 'appointment' && tx.reference?.id === appointment.id
    )

    if (!existingTransaction && appointment.price > 0) {
      const transaction = {
        id: `TX-${appointment.id}-${Date.now()}`,
        date: new Date(appointment.date),
        clientId: appointment.clientId,
        clientName: appointment.clientName,
        staffId: appointment.staffId,
        staffName: appointment.staffName,
        type: TransactionType.SERVICE_SALE,
        category: "Appointment Service",
        description: `Completed appointment - ${appointment.service}`,
        amount: appointment.price,
        paymentMethod: PaymentMethod.CASH,
        status: TransactionStatus.COMPLETED,
        location: appointment.location,
        source: TransactionSource.CALENDAR,
        reference: {
          type: "appointment",
          id: appointment.id
        },
        items: [
          {
            id: `service-${appointment.service.replace(/\s+/g, '-').toLowerCase()}`,
            name: appointment.service,
            quantity: 1,
            unitPrice: appointment.price,
            totalPrice: appointment.price,
            category: "Service"
          }
        ],
        metadata: {
          appointmentId: appointment.id,
          bookingReference: `VH-${appointment.id.toUpperCase()}`,
          appointmentDate: appointment.date,
          duration: appointment.duration,
          completedAt: appointment.statusHistory?.find((h: any) => h.status === 'completed')?.timestamp || new Date().toISOString()
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      existingTransactions.push(transaction)
      createdCount++

      console.log(`Created transaction for appointment ${appointment.id}: ${appointment.clientName} - ${appointment.service} - $${appointment.price}`)
    }
  })

  if (createdCount > 0) {
    localStorage.setItem('vanity_transactions', JSON.stringify(existingTransactions))
    console.log(`Created ${createdCount} transactions for completed appointments`)
  } else {
    console.log("No new transactions needed for completed appointments")
  }
}

// Initialize base transactions with default data
function initializeBaseTransactions() {
  const existingTransactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]')

  // Get the first location for transactions
  const locations = SettingsStorage.getLocations()
  const primaryLocationId = locations.length > 0 ? locations[0].id : "loc1"

  // Default base transactions for initial setup
  const defaultBaseTransactions = [
    {
      id: "TX-BASE-001",
      date: new Date("2025-03-28"),
      clientId: "jd1",
      clientName: "John Doe",
      type: TransactionType.PRODUCT_SALE,
      category: "Product Sale",
      description: "Hair Care Products - Shampoo & Conditioner",
      amount: 45.0,
      paymentMethod: PaymentMethod.CREDIT_CARD,
      status: TransactionStatus.COMPLETED,
      location: primaryLocationId,
      source: TransactionSource.POS,
      reference: {
        type: "pos_sale",
        id: "pos-001"
      },
      items: [
        {
          id: "product-shampoo-conditioner",
          name: "Hair Care Set",
          quantity: 1,
          unitPrice: 45.0,
          totalPrice: 45.0,
          category: "Product"
        }
      ],
      metadata: {
        posTransaction: true,
        productSale: true
      },
      createdAt: new Date("2025-03-28"),
      updatedAt: new Date()
    },
    {
      id: "TX-BASE-002",
      date: new Date("2025-03-29"),
      clientId: "js2",
      clientName: "Jane Smith",
      type: TransactionType.PRODUCT_SALE,
      category: "Product Sale",
      description: "Styling Products - Hair Gel & Spray",
      amount: 32.0,
      paymentMethod: PaymentMethod.CASH,
      status: TransactionStatus.COMPLETED,
      location: primaryLocationId,
      source: TransactionSource.POS,
      reference: {
        type: "pos_sale",
        id: "pos-002"
      },
      items: [
        {
          id: "product-styling-set",
          name: "Styling Products",
          quantity: 1,
          unitPrice: 32.0,
          totalPrice: 32.0,
          category: "Product"
        }
      ],
      metadata: {
        posTransaction: true,
        productSale: true
      },
      createdAt: new Date("2025-03-29"),
      updatedAt: new Date()
    }
  ]

  // Add base transactions if they don't already exist
  let addedCount = 0
  defaultBaseTransactions.forEach(baseTx => {
    const exists = existingTransactions.find((tx: any) => tx.id === baseTx.id)
    if (!exists) {
      existingTransactions.push(baseTx)
      addedCount++
    }
  })

  if (addedCount > 0) {
    localStorage.setItem('vanity_transactions', JSON.stringify(existingTransactions))
    console.log(`Added ${addedCount} base transactions`)
  } else {
    console.log("Base transactions already exist")
  }
}

// Initialize expenses data
function initializeExpenses() {
  const existingExpenses = localStorage.getItem('vanity_expenses')
  if (!existingExpenses) {
    console.log("Initializing expenses data...")

    // Default expenses for initial setup
    const defaultExpenses = [
      {
        id: "EXP-001",
        date: "2025-03-25",
        category: "Supplies",
        description: "Hair care products inventory",
        amount: 450.00,
        vendor: "Beauty Supply Co.",
        paymentMethod: "Credit Card",
        status: "Paid",
        location: "loc1"
      },
      {
        id: "EXP-002",
        date: "2025-03-26",
        category: "Utilities",
        description: "Monthly electricity bill",
        amount: 180.00,
        vendor: "Qatar Electric",
        paymentMethod: "Bank Transfer",
        status: "Paid",
        location: "loc1"
      },
      {
        id: "EXP-003",
        date: "2025-03-27",
        category: "Equipment",
        description: "Hair dryer maintenance",
        amount: 75.00,
        vendor: "Equipment Services",
        paymentMethod: "Cash",
        status: "Paid",
        location: "loc2"
      }
    ]

    localStorage.setItem('vanity_expenses', JSON.stringify(defaultExpenses))
    console.log(`Initialized ${defaultExpenses.length} expenses`)
  } else {
    console.log("Expenses data already exists")
  }
}

// Clear all data (for testing purposes)
export function clearAllData() {
  console.log("=== CLEARING ALL DATA ===")
  localStorage.removeItem('vanity_clients')
  localStorage.removeItem('vanity_staff')
  localStorage.removeItem('vanity_services')
  localStorage.removeItem('vanity_appointments')
  localStorage.removeItem('vanity_transactions')
  localStorage.removeItem('vanity_expenses')
  console.log("All data cleared")
}

// Reset and reinitialize all data
export function resetAndInitializeData() {
  console.log("=== RESETTING AND REINITIALIZING ALL DATA ===")
  clearAllData()
  initializeAllData()
}

// Get data statistics
export function getDataStatistics() {
  const clients = JSON.parse(localStorage.getItem('vanity_clients') || '[]')
  const staff = JSON.parse(localStorage.getItem('vanity_staff') || '[]')
  const services = JSON.parse(localStorage.getItem('vanity_services') || '[]')
  const appointments = JSON.parse(localStorage.getItem('vanity_appointments') || '[]')
  const transactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]')
  const expenses = JSON.parse(localStorage.getItem('vanity_expenses') || '[]')

  const completedAppointments = appointments.filter((apt: any) => apt.status === 'completed')
  const calendarTransactions = transactions.filter((tx: any) => tx.source === 'calendar')

  return {
    clients: clients.length,
    staff: staff.length,
    services: services.length,
    appointments: appointments.length,
    completedAppointments: completedAppointments.length,
    transactions: transactions.length,
    calendarTransactions: calendarTransactions.length,
    expenses: expenses.length
  }
}
