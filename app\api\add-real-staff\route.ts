import { NextRequest, NextResponse } from 'next/server';
import { StaffStorage } from '@/lib/staff-storage';

// Real staff data for the salon - EXACTLY matching the 7 staff members from HR system
const realStaffData = [
  {
    id: "staff-real-1",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+974 5555 1234",
    role: "stylist",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "MA",
    color: "bg-purple-100 text-purple-800",
    homeService: false,
    employeeNumber: "9100",
    dateOfBirth: "15-03-90", // DD-MM-YY format
    qidValidity: "31-12-25",
    passportValidity: "15-06-30",
    medicalValidity: "20-03-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-2",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+974 5555 2345",
    role: "stylist",
    locations: ["loc2"], // Medinat Khalifa
    status: "Active",
    avatar: "WT",
    color: "bg-blue-100 text-blue-800",
    homeService: false,
    employeeNumber: "9101",
    dateOfBirth: "22-07-88", // 1988-07-22
    qidValidity: "15-08-26",
    passportValidity: "20-11-29",
    medicalValidity: "10-05-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-3",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "+974 5555 3456",
    role: "nail_technician",
    locations: ["loc3"], // Muaither
    status: "Active",
    avatar: "MS",
    color: "bg-green-100 text-green-800",
    homeService: false,
    employeeNumber: "9102",
    dateOfBirth: "08-12-92", // 1992-12-08
    qidValidity: "30-04-25",
    passportValidity: "12-09-31",
    medicalValidity: "15-01-25",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-4",
    name: "Fatima Al-Zahra",
    email: "<EMAIL>",
    phone: "+974 5555 4567",
    role: "esthetician",
    locations: ["loc2"], // Medinat Khalifa
    status: "Active",
    avatar: "FZ",
    color: "bg-pink-100 text-pink-800",
    homeService: false,
    employeeNumber: "9103",
    dateOfBirth: "30-09-85", // 1985-09-30
    qidValidity: "22-07-24",
    passportValidity: "28-02-28",
    medicalValidity: "05-12-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-5",
    name: "Jane Mussa",
    email: "<EMAIL>",
    phone: "+974 5555 5678",
    role: "colorist",
    locations: ["loc3"], // Muaither
    status: "Active",
    avatar: "JM",
    color: "bg-yellow-100 text-yellow-800",
    homeService: false,
    employeeNumber: "9104",
    dateOfBirth: "14-06-87", // 1987-06-14
    qidValidity: "18-10-25",
    passportValidity: "14-03-30",
    medicalValidity: "22-08-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-6",
    name: "Aisha Mohammed",
    email: "<EMAIL>",
    phone: "+974 5555 6789",
    role: "receptionist",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "AM",
    color: "bg-indigo-100 text-indigo-800",
    homeService: false,
    employeeNumber: "9105",
    dateOfBirth: "25-11-95", // 1995-11-25
    qidValidity: "12-01-26",
    passportValidity: "08-07-32",
    medicalValidity: "18-04-25",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-7",
    name: "Aster Bekele",
    email: "<EMAIL>",
    phone: "+974-55664477",
    role: "stylist",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "AB",
    color: "bg-teal-100 text-teal-800",
    homeService: false,
    employeeNumber: "9106",
    dateOfBirth: "02-05-85", // 1985-05-02
    qidValidity: "31-12-25",
    passportValidity: "15-06-30",
    medicalValidity: "20-03-24",
    profileImage: "",
    profileImageType: ""
  }
];

/**
 * POST /api/add-real-staff
 * 
 * Add real staff data to localStorage (fallback storage)
 */
export async function POST() {
  try {
    console.log('Adding real staff data to localStorage...');
    
    // Clear existing staff data
    const existingStaff = StaffStorage.getStaff();
    console.log(`Found ${existingStaff.length} existing staff members`);
    
    // Add each staff member
    const addedStaff = [];
    for (const staffData of realStaffData) {
      try {
        // Check if staff member already exists
        const exists = existingStaff.find(s => s.email === staffData.email);
        if (exists) {
          console.log(`Staff member ${staffData.name} already exists, skipping...`);
          continue;
        }
        
        // Add staff member
        const newStaff = StaffStorage.addStaff(staffData);
        addedStaff.push(newStaff);
        console.log(`✅ Added staff member: ${staffData.name}`);
      } catch (error) {
        console.error(`❌ Error adding staff member ${staffData.name}:`, error);
      }
    }
    
    console.log(`Successfully added ${addedStaff.length} staff members to localStorage`);
    
    return NextResponse.json({
      success: true,
      addedCount: addedStaff.length,
      addedStaff: addedStaff.map(s => ({ id: s.id, name: s.name, email: s.email })),
      totalStaff: StaffStorage.getStaff().length
    });
    
  } catch (error) {
    console.error('Error adding real staff data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to add staff data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
