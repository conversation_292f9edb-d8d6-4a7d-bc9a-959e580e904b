"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ServiceStorage } from "@/lib/service-storage"
import { useServices } from "@/lib/service-provider"
import { useTransactions } from "@/lib/transaction-provider"
import { TransactionServiceMigration } from "@/lib/transaction-service-migration"
import { useToast } from "@/hooks/use-toast"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Database,
  RefreshCw,
  Search,
  Settings,
  BarChart3,
  Calendar,
  ShoppingCart
} from "lucide-react"

interface AuditResult {
  component: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: string[]
}

export default function DataIntegrationAuditPage() {
  const [auditResults, setAuditResults] = useState<AuditResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState('')
  const { services, categories, refreshServices, refreshCategories } = useServices()
  const { transactions } = useTransactions()
  const { toast } = useToast()

  // Run comprehensive data integration audit
  const runAudit = async () => {
    setIsRunning(true)
    setAuditResults([])
    const results: AuditResult[] = []

    try {
      // Test 1: Service Storage Verification
      setCurrentTest('Verifying Service Storage...')
      const storageServices = ServiceStorage.getServices()
      const storageCategories = ServiceStorage.getServiceCategories()
      
      results.push({
        component: 'Service Storage',
        status: storageServices.length === 144 ? 'pass' : 'fail',
        message: `Found ${storageServices.length} services (expected 144)`,
        details: [`Categories: ${storageCategories.length}`, `Real data loaded: ${storageServices.length > 20 ? 'Yes' : 'No'}`]
      })

      // Test 2: Service Provider Context
      setCurrentTest('Testing Service Provider Context...')
      results.push({
        component: 'Service Provider',
        status: services.length === storageServices.length ? 'pass' : 'warning',
        message: `Provider has ${services.length} services, Storage has ${storageServices.length}`,
        details: [`Categories in provider: ${categories.length}`, `Data sync: ${services.length === storageServices.length ? 'OK' : 'Mismatch'}`]
      })

      // Test 3: Category Consistency
      setCurrentTest('Checking Category Consistency...')
      const expectedCategories = ['Brading', 'Hair Extension', 'Styling', 'Hair Treatment', 'Color', 'Nail', 'Eyelash', 'Threading', 'Waxing', 'Henna', 'Massage And Spa']
      const actualCategories = categories.map(c => c.name)
      const missingCategories = expectedCategories.filter(cat => !actualCategories.includes(cat))
      
      results.push({
        component: 'Category System',
        status: missingCategories.length === 0 ? 'pass' : 'fail',
        message: `${actualCategories.length}/11 expected categories found`,
        details: missingCategories.length > 0 ? [`Missing: ${missingCategories.join(', ')}`] : ['All categories present']
      })

      // Test 4: Service Pricing Consistency
      setCurrentTest('Verifying Service Pricing...')
      const servicesWithoutPricing = services.filter(s => !s.price || s.price <= 0)
      results.push({
        component: 'Service Pricing',
        status: servicesWithoutPricing.length === 0 ? 'pass' : 'fail',
        message: `${services.length - servicesWithoutPricing.length}/${services.length} services have valid pricing`,
        details: servicesWithoutPricing.length > 0 ? [`Services without pricing: ${servicesWithoutPricing.map(s => s.name).join(', ')}`] : ['All services have valid pricing']
      })

      // Test 5: Location Availability
      setCurrentTest('Checking Location Availability...')
      const expectedLocations = ['loc1', 'loc2', 'loc3', 'home']
      const servicesWithIncompleteLocations = services.filter(s => 
        !s.locations || s.locations.length !== 4 || !expectedLocations.every(loc => s.locations.includes(loc))
      )
      
      results.push({
        component: 'Location Availability',
        status: servicesWithIncompleteLocations.length === 0 ? 'pass' : 'warning',
        message: `${services.length - servicesWithIncompleteLocations.length}/${services.length} services available in all locations`,
        details: servicesWithIncompleteLocations.length > 0 ? [`Services with incomplete locations: ${servicesWithIncompleteLocations.length}`] : ['All services available in all locations']
      })

      // Test 6: Transaction Integration
      setCurrentTest('Testing Transaction Integration...')
      const serviceTransactions = transactions.filter(t => t.type === 'service_sale')

      // Enhanced validation that checks multiple sources for service references
      const transactionsWithValidServices = serviceTransactions.filter(t => {
        // Check metadata.serviceId (new format)
        if (t.metadata?.serviceId && services.some(s => s.id === t.metadata?.serviceId)) {
          return true;
        }

        // Check metadata.serviceIds array (for multiple services)
        if (t.metadata?.serviceIds && Array.isArray(t.metadata.serviceIds)) {
          return t.metadata.serviceIds.some(serviceId =>
            services.some(s => s.id === serviceId)
          );
        }

        // Check items array for service references
        if (t.items && Array.isArray(t.items)) {
          return t.items.some(item => {
            // Check if item.serviceId exists and is valid
            if (item.serviceId && services.some(s => s.id === item.serviceId)) {
              return true;
            }
            // Check if item.id is a valid service ID (not prefixed)
            if (item.category === 'Service' && services.some(s => s.id === item.id)) {
              return true;
            }
            return false;
          });
        }

        return false;
      })

      results.push({
        component: 'Transaction Integration',
        status: serviceTransactions.length === 0 ? 'warning' : transactionsWithValidServices.length === serviceTransactions.length ? 'pass' : 'fail',
        message: serviceTransactions.length === 0 ? 'No service transactions found' : `${transactionsWithValidServices.length}/${serviceTransactions.length} service transactions have valid service references`,
        details: [
          `Total service transactions: ${serviceTransactions.length}`,
          `Valid service references: ${transactionsWithValidServices.length}`,
          `Validation checks: metadata.serviceId, metadata.serviceIds[], items[].serviceId, items[].id`
        ]
      })

      // Test 7: Category Service Counts
      setCurrentTest('Verifying Category Service Counts...')
      const categoryCountMismatches = categories.filter(cat => {
        const actualCount = services.filter(s => s.category === cat.id).length
        return actualCount !== cat.serviceCount
      })
      
      results.push({
        component: 'Category Service Counts',
        status: categoryCountMismatches.length === 0 ? 'pass' : 'warning',
        message: `${categories.length - categoryCountMismatches.length}/${categories.length} categories have correct service counts`,
        details: categoryCountMismatches.map(cat => {
          const actualCount = services.filter(s => s.category === cat.id).length
          return `${cat.name}: expected ${cat.serviceCount}, actual ${actualCount}`
        })
      })

      // Test 8: Data Persistence
      setCurrentTest('Testing Data Persistence...')
      const localStorageServices = localStorage.getItem('vanity_services')
      const localStorageCategories = localStorage.getItem('vanity_service_categories')
      
      results.push({
        component: 'Data Persistence',
        status: localStorageServices && localStorageCategories ? 'pass' : 'fail',
        message: `localStorage data: ${localStorageServices ? 'Services ✓' : 'Services ✗'} ${localStorageCategories ? 'Categories ✓' : 'Categories ✗'}`,
        details: [
          `Services in localStorage: ${localStorageServices ? JSON.parse(localStorageServices).length : 0}`,
          `Categories in localStorage: ${localStorageCategories ? JSON.parse(localStorageCategories).length : 0}`
        ]
      })

      setAuditResults(results)
      
      const passCount = results.filter(r => r.status === 'pass').length
      const failCount = results.filter(r => r.status === 'fail').length
      const warningCount = results.filter(r => r.status === 'warning').length
      
      toast({
        title: "Audit Complete",
        description: `${passCount} passed, ${warningCount} warnings, ${failCount} failed`,
        variant: failCount > 0 ? "destructive" : warningCount > 0 ? "default" : "default"
      })

    } catch (error) {
      console.error('Audit failed:', error)
      toast({
        title: "Audit Failed",
        description: "An error occurred during the audit. Check console for details.",
        variant: "destructive"
      })
    } finally {
      setIsRunning(false)
      setCurrentTest('')
    }
  }

  // Fix common issues
  const fixIssues = async () => {
    try {
      console.log('🔧 Starting comprehensive data integration fixes...');

      // Refresh service provider data
      await refreshServices()
      await refreshCategories()

      // Note: ServiceStorage.forceReinitialize methods are no longer needed
      // as we're now using database persistence

      // Fix transaction service references
      console.log('🔧 Fixing transaction service references...');
      const fixedTransactions = TransactionServiceMigration.fixAllTransactionServiceReferences(transactions);

      // Update transactions if any were fixed
      if (JSON.stringify(fixedTransactions) !== JSON.stringify(transactions)) {
        console.log('🔧 Updating transactions with fixed service references...');

        // Save the fixed transactions to localStorage
        localStorage.setItem('vanity_transactions', JSON.stringify(fixedTransactions));

        // Force refresh the transaction provider
        window.location.reload(); // Simple way to refresh all providers
      }

      toast({
        title: "Issues Fixed",
        description: "Attempted to fix common data integration issues including transaction service references. Run audit again to verify.",
      })

    } catch (error) {
      console.error('Fix issues error:', error);
      toast({
        title: "Fix Failed",
        description: "Failed to fix issues. Check console for details.",
        variant: "destructive"
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'border-green-200 bg-green-50'
      case 'fail':
        return 'border-red-200 bg-red-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Integration Audit</h1>
          <p className="text-muted-foreground">
            Comprehensive audit of service data integration across the salon management system
          </p>
        </div>
      </div>

      {/* Control Panel */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Run Audit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runAudit}
              disabled={isRunning}
              className="w-full"
              size="lg"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {currentTest || 'Running Audit...'}
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Start Audit
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Fix Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fixIssues}
              variant="outline"
              className="w-full"
              size="lg"
            >
              <Settings className="mr-2 h-4 w-4" />
              Auto Fix
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Current Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Services:</span>
                <Badge variant="outline">{services.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Categories:</span>
                <Badge variant="outline">{categories.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Transactions:</span>
                <Badge variant="outline">{transactions.length}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Audit Results */}
      {auditResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Audit Results</CardTitle>
            <CardDescription>
              Detailed analysis of data integration across all system components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {auditResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-start gap-3">
                    {getStatusIcon(result.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">{result.component}</h3>
                        <Badge 
                          variant={result.status === 'pass' ? 'default' : result.status === 'fail' ? 'destructive' : 'secondary'}
                        >
                          {result.status.toUpperCase()}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{result.message}</p>
                      {result.details && result.details.length > 0 && (
                        <ul className="text-xs text-muted-foreground mt-2 space-y-1">
                          {result.details.map((detail, idx) => (
                            <li key={idx}>• {detail}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      {auditResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {auditResults.filter(r => r.status === 'pass').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {auditResults.filter(r => r.status === 'warning').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Warnings</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-500" />
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {auditResults.filter(r => r.status === 'fail').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
