// MSW handlers for API mocking in tests
import { http, HttpResponse } from 'msw'

// Mock data
const mockAppointments = [
  {
    id: '1',
    bookingReference: '***********-1001',
    clientId: 'client1',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    staffId: 'staff1',
    staffName: '<PERSON>',
    service: 'Haircut & Style',
    serviceId: '1',
    date: '2024-12-16T10:00:00Z',
    duration: 60,
    location: 'loc1',
    price: 75,
    status: 'confirmed',
    notes: 'Regular client, prefers short styles',
  },
  {
    id: '2',
    bookingReference: '***********-1002',
    clientId: 'client2',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    staffId: 'staff2',
    staffName: '<PERSON>',
    service: 'Color & Highlights',
    serviceId: '2',
    date: '2024-12-16T14:00:00Z',
    duration: 120,
    location: 'loc1',
    price: 150,
    status: 'pending',
    notes: 'First time client',
  },
]

// Mock services removed - now using database API
const mockServices: any[] = []

// Mock staff data removed - using real staff data from FileStaffStorage instead
const mockStaff: any[] = []

const mockClients = [
  {
    id: 'client1',
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    phone: '(*************',
    preferences: 'Prefers morning appointments',
    notes: 'Regular client since 2023',
  },
  {
    id: 'client2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '(*************',
    preferences: 'Allergic to certain hair products',
    notes: 'New client, very detail-oriented',
  },
]

export const handlers = [
  // Appointments API
  http.get('/api/appointments', ({ request }) => {
    const url = new URL(request.url)
    const locationId = url.searchParams.get('locationId')
    const staffId = url.searchParams.get('staffId')
    const clientId = url.searchParams.get('clientId')
    
    let filteredAppointments = [...mockAppointments]
    
    if (locationId) {
      filteredAppointments = filteredAppointments.filter(apt => apt.location === locationId)
    }
    if (staffId) {
      filteredAppointments = filteredAppointments.filter(apt => apt.staffId === staffId)
    }
    if (clientId) {
      filteredAppointments = filteredAppointments.filter(apt => apt.clientId === clientId)
    }
    
    return HttpResponse.json({ appointments: filteredAppointments })
  }),

  http.post('/api/appointments', async ({ request }) => {
    const body = await request.json()
    const newAppointment = {
      id: `apt-${Date.now()}`,
      bookingReference: `VH-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      ...body,
      status: 'pending',
    }
    
    mockAppointments.push(newAppointment)
    return HttpResponse.json({ appointment: newAppointment }, { status: 201 })
  }),

  http.patch('/api/appointments/:id', async ({ params, request }) => {
    const { id } = params
    const body = await request.json()
    
    const appointmentIndex = mockAppointments.findIndex(apt => apt.id === id)
    if (appointmentIndex === -1) {
      return HttpResponse.json({ error: 'Appointment not found' }, { status: 404 })
    }
    
    mockAppointments[appointmentIndex] = { ...mockAppointments[appointmentIndex], ...body }
    return HttpResponse.json({ appointment: mockAppointments[appointmentIndex] })
  }),

  http.delete('/api/appointments/:id', ({ params }) => {
    const { id } = params
    const appointmentIndex = mockAppointments.findIndex(apt => apt.id === id)
    
    if (appointmentIndex === -1) {
      return HttpResponse.json({ error: 'Appointment not found' }, { status: 404 })
    }
    
    mockAppointments.splice(appointmentIndex, 1)
    return HttpResponse.json({ success: true })
  }),

  // Services API - now handled by database API endpoints
  // Mock handlers removed to use real database endpoints

  // Staff API
  http.get('/api/staff', () => {
    return HttpResponse.json({ staff: mockStaff })
  }),

  http.post('/api/staff', async ({ request }) => {
    const body = await request.json()
    const newStaff = {
      id: `staff-${Date.now()}`,
      ...body,
      status: 'Active',
    }
    
    mockStaff.push(newStaff)
    return HttpResponse.json({ staff: newStaff }, { status: 201 })
  }),

  // Clients API
  http.get('/api/clients', () => {
    return HttpResponse.json({ clients: mockClients })
  }),

  http.post('/api/clients', async ({ request }) => {
    const body = await request.json()
    const newClient = {
      id: `client-${Date.now()}`,
      ...body,
    }
    
    mockClients.push(newClient)
    return HttpResponse.json({ client: newClient }, { status: 201 })
  }),

  // Client Portal API
  http.get('/api/client-portal/appointments', ({ request }) => {
    const url = new URL(request.url)
    const clientId = url.searchParams.get('clientId')
    
    const clientAppointments = mockAppointments.filter(apt => apt.clientId === clientId)
    return HttpResponse.json({ appointments: clientAppointments })
  }),

  http.get('/api/client-portal/loyalty', ({ request }) => {
    const url = new URL(request.url)
    const clientId = url.searchParams.get('clientId')
    
    return HttpResponse.json({
      loyalty: {
        clientId,
        points: 250,
        tier: 'Silver',
        rewards: [],
        history: [],
      }
    })
  }),

  // Auth API
  http.post('/api/auth/signin', async ({ request }) => {
    const body = await request.json()
    const { email, password } = body
    
    // Mock authentication
    if (email === '<EMAIL>' && password === 'password') {
      return HttpResponse.json({
        user: {
          id: 'admin1',
          email: '<EMAIL>',
          role: 'admin',
          name: 'Admin User',
        },
        token: 'mock-jwt-token',
      })
    }
    
    return HttpResponse.json({ error: 'Invalid credentials' }, { status: 401 })
  }),

  // Error simulation for testing error handling
  http.get('/api/error-test', () => {
    return HttpResponse.json({ error: 'Simulated server error' }, { status: 500 })
  }),
]
