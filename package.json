{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev", "build": "next build", "start": "set NODE_ENV=production && node server.js", "start:next": "next start", "lint": "eslint --config eslint.config.mjs .", "lint:fix": "eslint --config eslint.config.mjs --fix .", "lint:next": "next lint", "clean-start": "node clean-start.js 3030", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@auth/core": "latest", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "latest", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-visually-hidden": "^1.2.3", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.23", "date-fns": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-auth": "latest", "next-themes": "latest", "nodemailer": "latest", "pg": "^8.16.0", "prisma": "^6.8.2", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "web-vitals": "^3.5.2", "zod": "^3.25.36"}, "devDependencies": {"@eslint/js": "^9.0.0", "@next/eslint-plugin-next": "^15.2.4", "@storybook/addon-essentials": "^7.6.20", "@storybook/nextjs": "^7.6.20", "@storybook/react": "^7.6.20", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/pg": "^8.15.2", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.6", "playwright": "^1.52.0", "postcss": "^8", "prisma-erd-generator": "^2.0.4", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5", "typescript-eslint": "^7.4.0"}}