"use client"

// Storage key for localStorage
const STORAGE_KEY = "vanity_staff"

// Staff interface
export interface StaffMember {
  id: string
  name: string
  role: string
  email: string
  phone: string
  locations: string[]
  status: "Active" | "Inactive"
  avatar?: string
  profileImage?: string
  color?: string
  homeService?: boolean
  dateOfBirth?: string // Date of birth in YYYY-MM-DD format
  specialties?: string[]
  yearsExperience?: number
  rating?: number
  bio?: string
  isFeatured?: boolean
  certifications?: string[]
  languages?: string[]
  createdAt?: string
  updatedAt?: string
}

// Default staff data - REMOVED: Using real data from FileStaffStorage instead
const defaultStaff: StaffMember[] = [
]

// Helper function to get data from localStorage
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const storedValue = localStorage.getItem(key)
    if (!storedValue) {
      return defaultValue
    }

    const parsedValue = JSON.parse(storedValue)
    
    // For arrays, ensure we actually got an array back
    if (Array.isArray(defaultValue) && !Array.isArray(parsedValue)) {
      console.warn(`Expected array for ${key} but got ${typeof parsedValue}`)
      return defaultValue
    }

    return parsedValue as T
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error)
    return defaultValue
  }
}

// Helper function to save data to localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// Staff Data Service
export const StaffDataService = {
  // Initialize staff with default data if none exists
  initializeStaff: (): StaffMember[] => {
    console.log("StaffDataService: Initializing staff...")
    
    const existingStaff = getFromStorage<StaffMember[]>(STORAGE_KEY, [])
    if (existingStaff.length > 0) {
      console.log("StaffDataService: Found existing staff:", existingStaff.length)
      return existingStaff
    }
    
    console.log("StaffDataService: No existing staff found, initializing with defaults")
    const staffWithTimestamps = defaultStaff.map(staff => ({
      ...staff,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))
    
    saveToStorage(STORAGE_KEY, staffWithTimestamps)
    console.log("StaffDataService: Initialized", staffWithTimestamps.length, "default staff members")
    return staffWithTimestamps
  },

  // Get all staff members
  getStaff: (): StaffMember[] => {
    const staff = getFromStorage<StaffMember[]>(STORAGE_KEY, [])
    console.log("StaffDataService.getStaff: Retrieved", staff.length, "staff members from localStorage")

    // Only auto-initialize if localStorage is completely empty (null/undefined)
    // Don't auto-initialize if we have an empty array, as that might be intentional
    const rawData = localStorage.getItem(STORAGE_KEY)
    if (rawData === null || rawData === undefined) {
      console.log("StaffDataService.getStaff: No data found, auto-initializing...")
      return StaffDataService.initializeStaff()
    }

    return staff
  },

  // Get staff member by ID
  getStaffById: (id: string): StaffMember | undefined => {
    const staff = StaffDataService.getStaff()
    return staff.find(member => member.id === id)
  },

  // Get staff by location
  getStaffByLocation: (locationId: string): StaffMember[] => {
    const staff = StaffDataService.getStaff()
    return staff.filter(member => 
      member.locations.includes(locationId) && member.status === "Active"
    )
  },

  // Get staff by role
  getStaffByRole: (role: string): StaffMember[] => {
    const staff = StaffDataService.getStaff()
    return staff.filter(member => 
      member.role === role && member.status === "Active"
    )
  },

  // Save all staff
  saveStaff: (staff: StaffMember[]) => {
    saveToStorage(STORAGE_KEY, staff)
  },

  // Add new staff member
  addStaff: (staffMember: Omit<StaffMember, "id" | "createdAt" | "updatedAt">): StaffMember => {
    const staff = StaffDataService.getStaff()
    const newStaff: StaffMember = {
      ...staffMember,
      id: `staff_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    staff.push(newStaff)
    saveToStorage(STORAGE_KEY, staff)
    console.log("StaffDataService: Added new staff member:", newStaff.name)
    return newStaff
  },

  // Update staff member
  updateStaff: (id: string, updates: Partial<StaffMember>): StaffMember | null => {
    const staff = StaffDataService.getStaff()
    const index = staff.findIndex(member => member.id === id)
    
    if (index !== -1) {
      staff[index] = {
        ...staff[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      saveToStorage(STORAGE_KEY, staff)
      console.log("StaffDataService: Updated staff member:", staff[index].name)
      return staff[index]
    }
    
    console.warn("StaffDataService: Staff member not found for update:", id)
    return null
  },

  // Delete staff member
  deleteStaff: (id: string): boolean => {
    const staff = StaffDataService.getStaff()
    const filteredStaff = staff.filter(member => member.id !== id)
    
    if (filteredStaff.length < staff.length) {
      saveToStorage(STORAGE_KEY, filteredStaff)
      console.log("StaffDataService: Deleted staff member:", id)
      return true
    }
    
    console.warn("StaffDataService: Staff member not found for deletion:", id)
    return false
  },

  // Get active staff count
  getActiveStaffCount: (): number => {
    const staff = StaffDataService.getStaff()
    return staff.filter(member => member.status === "Active").length
  },

  // Get staff roles
  getStaffRoles: (): string[] => {
    const staff = StaffDataService.getStaff()
    const roles = [...new Set(staff.map(member => member.role))]
    return roles.sort()
  }
}
