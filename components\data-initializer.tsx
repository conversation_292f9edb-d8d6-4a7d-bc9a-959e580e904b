"use client"

import { useEffect, useState } from 'react'
import { performDataMigration, isMigrationNeeded } from '@/lib/data-migration-service'
import { CurrencyIntegration } from '@/components/currency/currency-integration'

export function DataInitializer() {
  const [migrationStatus, setMigrationStatus] = useState<'pending' | 'running' | 'complete' | 'error'>('pending')

  useEffect(() => {
    // Perform data migration on app startup with a delay to not block initial render
    const performMigration = async () => {
      // Add a small delay to allow the page to render first
      await new Promise(resolve => setTimeout(resolve, 100))

      console.log("DataInitializer: Starting data migration...")

      try {
        // Check if migration is needed
        if (isMigrationNeeded()) {
          console.log("DataInitializer: Migration needed, starting migration process...")
          setMigrationStatus('running')

          // Run migration in the background
          performDataMigration().then(() => {
            console.log("DataInitializer: Data migration completed successfully")
            setMigrationStatus('complete')
          }).catch((error) => {
            console.error("DataInitializer: Error during data migration:", error)
            setMigrationStatus('error')
          })
        } else {
          console.log("DataInitializer: No migration needed, data is already up to date")
          setMigrationStatus('complete')
        }
      } catch (error) {
        console.error("DataInitializer: Error during data migration:", error)
        setMigrationStatus('error')
      }
    }

    performMigration()
  }, [])

  // This component includes currency integration and doesn't render anything visible
  return (
    <>
      <CurrencyIntegration />
      {/* Migration status could be used for loading indicators in the future */}
    </>
  )
}
