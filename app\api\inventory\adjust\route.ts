import { NextResponse } from "next/server"
import { productsRepository } from "@/lib/db"
import { ServerInventoryStorage } from "@/lib/server-inventory-storage"

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.productId || !data.locationId || !data.quantity || !data.adjustmentType || !data.reason) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const productId = Number.parseInt(data.productId)
    const locationId = Number.parseInt(data.locationId)
    let quantity = Number.parseInt(data.quantity)

    // If removing stock, make quantity negative
    if (data.adjustmentType === "remove") {
      quantity = -quantity
    }

    try {
      // Try to update real database
      const updatedInventory = await productsRepository.updateInventory(productId, locationId, quantity)

      // Record inventory transaction
      const { query } = await import("@/lib/db")

      await query(
        `INSERT INTO inventory_transactions
          (product_id, location_id, quantity, transaction_type, notes, created_by)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [productId, locationId, quantity, data.reason, data.notes || null, data.userId || null],
      )

      return NextResponse.json({
        success: true,
        inventory: updatedInventory,
      })
    } catch (dbError) {
      // If database fails, use server-side file storage
      console.log("Database not available, using server-side file storage for stock adjustment")

      try {
        // Map location ID to location string
        const locationMap: { [key: string]: string } = {
          "1": "loc1",
          "2": "loc2",
          "3": "loc3"
        }

        const mappedLocationId = locationMap[data.locationId] || "loc1"

        // Find the product by ID (convert numeric ID to string format)
        const products = await ServerInventoryStorage.getProducts(mappedLocationId)
        const product = products.find(p =>
          p.id === data.productId ||
          p.id === `prod-${data.productId}` ||
          parseInt(p.id.replace(/\D/g, '')) === productId
        )

        if (!product) {
          return NextResponse.json({ error: "Product not found" }, { status: 404 })
        }

        // Perform the stock adjustment
        const success = await ServerInventoryStorage.adjustStock(
          product.id,
          mappedLocationId,
          quantity,
          data.reason,
          data.notes
        )

        if (!success) {
          return NextResponse.json({ error: "Failed to adjust stock" }, { status: 500 })
        }

        return NextResponse.json({
          success: true,
          message: "Stock adjustment completed using server-side file storage",
          adjustment: {
            productId: product.id,
            locationId: mappedLocationId,
            quantity,
            newQuantity: Math.max(0, product.quantity + quantity)
          }
        })
      } catch (storageError) {
        console.error("Error with server storage adjustment:", storageError)
        return NextResponse.json({ error: "Failed to adjust stock in storage" }, { status: 500 })
      }
    }
  } catch (error) {
    console.error("Error adjusting inventory:", error)
    return NextResponse.json({ error: "Failed to adjust inventory" }, { status: 500 })
  }
}

