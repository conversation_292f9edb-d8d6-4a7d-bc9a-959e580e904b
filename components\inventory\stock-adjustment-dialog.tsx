"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

interface StockAdjustmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: any
  onStockAdjusted?: () => void
}

export function StockAdjustmentDialog({ open, onOpenChange, product, onStockAdjusted }: StockAdjustmentDialogProps) {
  const { currentLocation } = useAuth()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    adjustmentType: "add",
    quantity: "1",
    reason: "purchase",
    notes: "",
  })

  useEffect(() => {
    if (open && product) {
      // Reset form when dialog opens
      setFormData({
        adjustmentType: "add",
        quantity: "1",
        reason: "purchase",
        notes: "",
      })
    }
  }, [open, product])

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!product) return

    setIsSubmitting(true)

    try {
      // Call the actual API to adjust stock
      const adjustmentData = {
        productId: product.id,
        locationId: currentLocation === "all" ? "loc1" : currentLocation, // Default to loc1 if "all" is selected
        adjustmentType: formData.adjustmentType,
        quantity: Number.parseInt(formData.quantity),
        reason: formData.reason,
        notes: formData.notes,
      }

      const response = await fetch("/api/inventory/adjust", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(adjustmentData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to adjust stock")
      }

      const result = await response.json()

      toast({
        title: "Stock adjusted",
        description: `${product.name} stock has been ${formData.adjustmentType === "add" ? "increased" : "decreased"} by ${formData.quantity}.`,
      })

      // Call the callback to refresh inventory data
      if (onStockAdjusted) {
        onStockAdjusted()
      }

      onOpenChange(false)
    } catch (error) {
      console.error("Failed to adjust stock:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to adjust stock. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Adjust Stock: {product.name}</DialogTitle>
            <DialogDescription>Current stock: {product.stock} units</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="adjustmentType">Adjustment Type</Label>
                <Select
                  value={formData.adjustmentType}
                  onValueChange={(value) => handleChange("adjustmentType", value)}
                >
                  <SelectTrigger id="adjustmentType">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="add">Add Stock</SelectItem>
                    <SelectItem value="remove">Remove Stock</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={(e) => handleChange("quantity", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reason">Reason</Label>
              <Select value={formData.reason} onValueChange={(value) => handleChange("reason", value)}>
                <SelectTrigger id="reason">
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  {formData.adjustmentType === "add" ? (
                    <>
                      <SelectItem value="purchase">New Purchase</SelectItem>
                      <SelectItem value="return">Customer Return</SelectItem>
                      <SelectItem value="transfer">Transfer In</SelectItem>
                      <SelectItem value="correction">Inventory Correction</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="sale">Sale</SelectItem>
                      <SelectItem value="damage">Damaged/Expired</SelectItem>
                      <SelectItem value="transfer">Transfer Out</SelectItem>
                      <SelectItem value="correction">Inventory Correction</SelectItem>
                      <SelectItem value="professional">Professional Use</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adjusting..." : "Adjust Stock"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

