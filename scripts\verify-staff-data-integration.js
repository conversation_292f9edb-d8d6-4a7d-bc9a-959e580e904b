#!/usr/bin/env node

/**
 * Staff Data Integration Verification Script
 * 
 * This script verifies that all mock staff data has been removed from the appointments
 * calendar system and that all components are using real staff data from the HR system.
 */

const fs = require('fs');
const path = require('path');

// Files to check for mock staff data usage
const filesToCheck = [
  'components/scheduling/salon-calendar-view.tsx',
  'components/scheduling/enhanced-salon-calendar.tsx', 
  'components/scheduling/appointment-calendar.tsx',
  'components/scheduling/staff-availability.tsx',
  'components/scheduling/new-appointment-dialog-v2.tsx',
  'components/scheduling/group-appointment-dialog.tsx',
  'components/scheduling/blocked-time-dialog.tsx',
  'components/scheduling/enhanced-booking-summary.tsx',
  'components/new-appointment-dialog.tsx',
  'components/scheduling/enhanced-new-appointment-dialog.tsx',
  'lib/mock-data.ts',
  'src/mocks/handlers.ts'
];

// Patterns that indicate mock staff data usage
const mockDataPatterns = [
  /import.*mockStaff.*from/,
  /mockStaff\s*\[/,
  /mockStaff\.map/,
  /mockStaff\.find/,
  /mockStaff\.filter/,
  /const\s+mockStaff\s*=\s*\[.*{/,
  /export\s+const\s+mockStaff\s*=\s*\[.*{/,
  /staff.*=.*\[.*{.*id.*name.*role/,
  /hardcoded.*staff.*array/i,
  /fallback.*staff.*data/i
];

// Patterns that indicate proper real staff data usage
const realDataPatterns = [
  /useStaff\(\)/,
  /useApiStaff\(\)/,
  /StaffStorage\.getStaff\(\)/,
  /staff.*from.*HR.*system/i,
  /real.*staff.*data/i,
  /NO.*MOCK.*DATA/i
];

console.log('🔍 STAFF DATA INTEGRATION VERIFICATION');
console.log('=====================================\n');

let totalIssues = 0;
let totalFiles = 0;
let filesWithIssues = 0;

filesToCheck.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  totalFiles++;
  const content = fs.readFileSync(fullPath, 'utf8');
  const lines = content.split('\n');
  
  let fileIssues = 0;
  let hasRealDataUsage = false;
  
  console.log(`📁 Checking: ${filePath}`);
  
  // Check for mock data patterns
  lines.forEach((line, index) => {
    mockDataPatterns.forEach(pattern => {
      if (pattern.test(line)) {
        console.log(`  ❌ Line ${index + 1}: Mock staff data detected: ${line.trim()}`);
        fileIssues++;
        totalIssues++;
      }
    });
    
    // Check for real data usage
    realDataPatterns.forEach(pattern => {
      if (pattern.test(line)) {
        hasRealDataUsage = true;
      }
    });
  });
  
  // Special checks for specific files
  if (filePath.includes('mock-data.ts')) {
    // Check if mockStaff is properly emptied
    if (content.includes('export const mockStaff: any[] = []')) {
      console.log(`  ✅ mockStaff properly emptied in mock-data.ts`);
    } else if (content.includes('mockStaff') && !content.includes('mockStaff: any[] = []')) {
      console.log(`  ❌ mockStaff not properly emptied in mock-data.ts`);
      fileIssues++;
      totalIssues++;
    }
  }
  
  if (filePath.includes('handlers.ts')) {
    // Check if mock staff handlers are properly emptied
    if (content.includes('const mockStaff: any[] = []')) {
      console.log(`  ✅ mockStaff properly emptied in handlers.ts`);
    } else if (content.includes('mockStaff') && !content.includes('mockStaff: any[] = []')) {
      console.log(`  ❌ mockStaff not properly emptied in handlers.ts`);
      fileIssues++;
      totalIssues++;
    }
  }
  
  // Check if calendar/appointment components use real data
  if (filePath.includes('calendar') || filePath.includes('appointment') || filePath.includes('staff')) {
    if (!hasRealDataUsage) {
      console.log(`  ⚠️  No real staff data usage detected - verify manually`);
    } else {
      console.log(`  ✅ Real staff data usage detected`);
    }
  }
  
  if (fileIssues === 0) {
    console.log(`  ✅ No mock staff data issues found`);
  } else {
    filesWithIssues++;
  }
  
  console.log('');
});

// Summary
console.log('📊 VERIFICATION SUMMARY');
console.log('======================');
console.log(`Total files checked: ${totalFiles}`);
console.log(`Files with issues: ${filesWithIssues}`);
console.log(`Total issues found: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 SUCCESS: All mock staff data has been removed!');
  console.log('✅ All components are using real staff data from HR system');
  process.exit(0);
} else {
  console.log('\n❌ ISSUES FOUND: Mock staff data still exists in the system');
  console.log('🔧 Please review and fix the issues listed above');
  process.exit(1);
}
