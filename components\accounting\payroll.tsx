"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Pop<PERSON>, <PERSON>over<PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import type { DateRange } from "react-day-picker"
import { useStaff } from "@/lib/use-staff-data"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import {
  Download,
  Eye,
  MoreHorizontal,
  Plus,
  FileText,
  DollarSign,
  Printer,
  CheckCircle2,
  Calendar as CalendarIcon,
  FileSpreadsheet,
  File as FilePdf,
  Mail,
  Edit,
  Save
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"

interface PayrollProps {
  dateRange?: DateRange
}

// Mock additional costs data
const mockAdditionalCosts = {
  visa: {
    stylist: 1200,
    colorist: 1200,
    barber: 1000,
    nail_technician: 1000,
    esthetician: 1000,
    receptionist: 800
  },
  manpower: {
    stylist: 800,
    colorist: 800,
    barber: 700,
    nail_technician: 700,
    esthetician: 700,
    receptionist: 600
  },
  medical: {
    stylist: 1500,
    colorist: 1500,
    barber: 1500,
    nail_technician: 1500,
    esthetician: 1500,
    receptionist: 1500
  },
  idCost: {
    stylist: 300,
    colorist: 300,
    barber: 300,
    nail_technician: 300,
    esthetician: 300,
    receptionist: 300
  },
  accommodation: {
    stylist: 1200,
    colorist: 1200,
    barber: 1000,
    nail_technician: 1000,
    esthetician: 1000,
    receptionist: 800
  },
  transportation: {
    stylist: 1500,
    colorist: 1500,
    barber: 1200,
    nail_technician: 1200,
    esthetician: 1200,
    receptionist: 1000
  },
  otherCosts: {
    stylist: 500,
    colorist: 500,
    barber: 400,
    nail_technician: 400,
    esthetician: 400,
    receptionist: 300
  }
}

export function Payroll({ dateRange }: PayrollProps) {
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const { staff: realStaff } = useStaff()
  const [activeTab, setActiveTab] = useState("basic-payroll")
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [selectedStaff, setSelectedStaff] = useState<any>(null)
  const [isAddCostDialogOpen, setIsAddCostDialogOpen] = useState(false)
  const [isProcessPaymentDialogOpen, setIsProcessPaymentDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isPayStubDialogOpen, setIsPayStubDialogOpen] = useState(false)
  const [isEditCostDialogOpen, setIsEditCostDialogOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState("excel")
  const [exportData, setExportData] = useState("current")
  const [paymentDate, setPaymentDate] = useState<Date>(new Date())
  const [paymentMethod, setPaymentMethod] = useState("bank_transfer")
  const [paymentNote, setPaymentNote] = useState("")
  const [sendEmail, setSendEmail] = useState(true)
  const [newCost, setNewCost] = useState({
    staffId: "",
    costType: "visa",
    amount: 0,
    description: "",
    date: format(new Date(), "yyyy-MM-dd")
  })

  // Initialize payroll data state
  const [payrollData, setPayrollData] = useState(() => {
    // Generate initial payroll data based on real staff
    return realStaff
      .map((staff) => {
        // Use staff ID as seed for consistent data
        const seed = parseInt(staff.id.replace(/\D/g, '')) || 1
        return {
          id: `PR-${staff.id}`,
          staffId: staff.id,
          staffName: staff.name,
          role: staff.role,
          regularHours: Math.floor(20 + (seed * 123) % 20) + 20, // 20-40 hours
          overtimeHours: Math.floor((seed * 456) % 10), // 0-10 hours
          commissions: Math.floor(100 + (seed * 789) % 500), // 100-600 commissions
          grossPay: 0,
          deductions: 0,
          netPay: 0,
          status: "Pending",
        }
      })
      .map((item) => {
        const hourlyRate =
          item.role === "stylist" || item.role === "colorist"
            ? 25
            : item.role === "barber"
              ? 22
              : item.role === "nail_technician" || item.role === "esthetician"
                ? 20
                : 18

        const regularPay = item.regularHours * hourlyRate
        const overtimePay = item.overtimeHours * (hourlyRate * 1.5)
        const grossPay = regularPay + overtimePay + item.commissions
        const deductions = grossPay * 0.2 // 20% for taxes and deductions
        const netPay = grossPay - deductions

        return {
          ...item,
          hourlyRate,
          regularPay: Number.parseFloat(regularPay.toFixed(2)),
          overtimePay: Number.parseFloat(overtimePay.toFixed(2)),
          grossPay: Number.parseFloat(grossPay.toFixed(2)),
          deductions: Number.parseFloat(deductions.toFixed(2)),
          netPay: Number.parseFloat(netPay.toFixed(2)),
        }
      })
  })

  // Initialize cost to company data state
  const [costToCompanyData, setCostToCompanyData] = useState(() => {
    // Generate initial cost to company data
    return payrollData.map(item => {
      const role = item.role as keyof typeof mockAdditionalCosts.visa

      // Calculate monthly costs (annual costs divided by 12)
      const visaCost = mockAdditionalCosts.visa[role] / 12
      const manpowerCost = mockAdditionalCosts.manpower[role] / 12
      const medicalCost = mockAdditionalCosts.medical[role] / 12
      const idCost = mockAdditionalCosts.idCost[role] / 12
      const accommodationCost = mockAdditionalCosts.accommodation[role] / 12
      const transportationCost = mockAdditionalCosts.transportation[role] / 12
      const otherCosts = mockAdditionalCosts.otherCosts[role] / 12

      const totalAdditionalCosts =
        visaCost +
        manpowerCost +
        medicalCost +
        idCost +
        accommodationCost +
        transportationCost +
        otherCosts

      const totalCostToCompany = item.grossPay + totalAdditionalCosts

      return {
        ...item,
        visaCost: Number.parseFloat(visaCost.toFixed(2)),
        manpowerCost: Number.parseFloat(manpowerCost.toFixed(2)),
        medicalCost: Number.parseFloat(medicalCost.toFixed(2)),
        idCost: Number.parseFloat(idCost.toFixed(2)),
        accommodationCost: Number.parseFloat(accommodationCost.toFixed(2)),
        transportationCost: Number.parseFloat(transportationCost.toFixed(2)),
        otherCosts: Number.parseFloat(otherCosts.toFixed(2)),
        totalAdditionalCosts: Number.parseFloat(totalAdditionalCosts.toFixed(2)),
        totalCostToCompany: Number.parseFloat(totalCostToCompany.toFixed(2))
      }
    })
  })

  const handleViewDetails = (staff: any) => {
    setSelectedStaff(staff)
    setIsDetailsDialogOpen(true)
  }

  const handleProcessPayment = (staff: any) => {
    setSelectedStaff(staff)
    setIsProcessPaymentDialogOpen(true)
  }

  const handlePayStub = (staff: any) => {
    setSelectedStaff(staff)
    setIsPayStubDialogOpen(true)
  }

  const handleExport = () => {
    setIsExportDialogOpen(true)
  }

  const processPayment = () => {
    // In a real app, this would process the payment through an API
    const updatedPayrollData = payrollData.map(item => {
      if (item.id === selectedStaff.id) {
        return {
          ...item,
          status: "Paid"
        }
      }
      return item
    })

    // Update the cost to company data as well
    const updatedCostToCompanyData = costToCompanyData.map(item => {
      if (item.id === selectedStaff.id) {
        return {
          ...item,
          status: "Paid"
        }
      }
      return item
    })

    // Update the state with the new data
    setPayrollData(updatedPayrollData)
    setCostToCompanyData(updatedCostToCompanyData)

    // Close the dialog
    setIsProcessPaymentDialogOpen(false)

    toast({
      title: "Payment processed",
      description: `Payment for ${selectedStaff.staffName} has been processed successfully.`,
    })
  }

  const downloadPayStub = () => {
    // In a real app, this would generate and download a PDF
    toast({
      title: "Pay stub downloaded",
      description: `Pay stub for ${selectedStaff.staffName} has been downloaded.`,
    })
    setIsPayStubDialogOpen(false)
  }

  const exportPayroll = () => {
    // In a real app, this would generate and download the selected format
    const formatName = exportFormat === "excel" ? "Excel" : exportFormat === "pdf" ? "PDF" : "CSV"
    const dataScope = exportData === "current" ? "current view" : "all staff"

    toast({
      title: `Export complete`,
      description: `Payroll data has been exported to ${formatName} format for ${dataScope}.`,
    })
    setIsExportDialogOpen(false)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Payroll & Staff Costs</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAddCostDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Cost
          </Button>
          <Button onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="basic-payroll">Basic Payroll</TabsTrigger>
            <TabsTrigger value="cost-to-company">Cost to Company</TabsTrigger>
          </TabsList>

          <TabsContent value="basic-payroll">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-lg font-medium">Staff Payroll</h3>
                <p className="text-sm text-muted-foreground">Manage staff salaries, commissions, and payments</p>
              </div>
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="text-right">Regular Hours</TableHead>
                    <TableHead className="text-right">Overtime Hours</TableHead>
                    <TableHead className="text-right">Commissions</TableHead>
                    <TableHead className="text-right">Gross Pay</TableHead>
                    <TableHead className="text-right">Deductions</TableHead>
                    <TableHead className="text-right">Net Pay</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payrollData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.staffName}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {item.role === "stylist"
                            ? "Stylist"
                            : item.role === "colorist"
                              ? "Colorist"
                              : item.role === "nail_technician"
                                ? "Nail Technician"
                                : item.role === "esthetician"
                                  ? "Esthetician"
                                  : item.role === "barber"
                                    ? "Barber"
                                    : "Receptionist"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">{item.regularHours}</TableCell>
                      <TableCell className="text-right">{item.overtimeHours}</TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.commissions} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.grossPay} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.deductions} /></TableCell>
                      <TableCell className="text-right font-medium"><CurrencyDisplay amount={item.netPay} /></TableCell>
                      <TableCell>
                        <Badge variant={item.status === "Paid" ? "success" : "outline"}>{item.status}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(item)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setSelectedStaff(item)
                              setIsEditCostDialogOpen(true)
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit costs
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleProcessPayment(item)} disabled={item.status === "Paid"}>
                              <CheckCircle2 className="mr-2 h-4 w-4" />
                              Process payment
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handlePayStub(item)}>
                              <FilePdf className="mr-2 h-4 w-4" />
                              Download pay stub
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setSelectedStaff(item)
                              toast({
                                title: "Email sent",
                                description: `Pay details have been emailed to ${item.staffName}.`,
                              })
                            }}>
                              <Mail className="mr-2 h-4 w-4" />
                              Email pay details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="cost-to-company">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-lg font-medium">Cost to Company</h3>
                <p className="text-sm text-muted-foreground">Manage additional costs and total cost to company</p>
              </div>
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="text-right">Gross Pay</TableHead>
                    <TableHead className="text-right">Visa</TableHead>
                    <TableHead className="text-right">Manpower</TableHead>
                    <TableHead className="text-right">Medical</TableHead>
                    <TableHead className="text-right">ID Costs</TableHead>
                    <TableHead className="text-right">Accommodation</TableHead>
                    <TableHead className="text-right">Transportation</TableHead>
                    <TableHead className="text-right">Other Costs</TableHead>
                    <TableHead className="text-right">Total CTC</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {costToCompanyData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.staffName}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {item.role === "stylist"
                            ? "Stylist"
                            : item.role === "colorist"
                              ? "Colorist"
                              : item.role === "nail_technician"
                                ? "Nail Technician"
                                : item.role === "esthetician"
                                  ? "Esthetician"
                                  : item.role === "barber"
                                    ? "Barber"
                                    : "Receptionist"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.grossPay} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.visaCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.manpowerCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.medicalCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.idCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.accommodationCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.transportationCost} /></TableCell>
                      <TableCell className="text-right"><CurrencyDisplay amount={item.otherCosts} /></TableCell>
                      <TableCell className="text-right font-medium"><CurrencyDisplay amount={item.totalCostToCompany} /></TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(item)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View cost details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setSelectedStaff(item)
                              setIsEditCostDialogOpen(true)
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit costs
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleProcessPayment(item)} disabled={item.status === "Paid"}>
                              <CheckCircle2 className="mr-2 h-4 w-4" />
                              Process payment
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                              setSelectedStaff(item)
                              toast({
                                title: "Report generated",
                                description: `Cost report for ${item.staffName} has been generated.`,
                              })
                            }}>
                              <FileText className="mr-2 h-4 w-4" />
                              Generate cost report
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              setSelectedStaff(item)
                              toast({
                                title: "Report exported",
                                description: `Cost breakdown for ${item.staffName} has been exported to Excel.`,
                              })
                            }}>
                              <FileSpreadsheet className="mr-2 h-4 w-4" />
                              Export to Excel
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>

        {/* Staff Details Dialog */}
        {selectedStaff && (
          <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Staff Cost Details</DialogTitle>
                <DialogDescription>
                  Detailed cost breakdown for {selectedStaff.staffName}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">Staff Name</Label>
                    <p className="font-medium">{selectedStaff.staffName}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Role</Label>
                    <p>
                      <Badge variant="outline">
                        {selectedStaff.role === "stylist"
                          ? "Stylist"
                          : selectedStaff.role === "colorist"
                            ? "Colorist"
                            : selectedStaff.role === "nail_technician"
                              ? "Nail Technician"
                              : selectedStaff.role === "esthetician"
                                ? "Esthetician"
                                : selectedStaff.role === "barber"
                                  ? "Barber"
                                  : "Receptionist"}
                      </Badge>
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">Salary Details</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span>Hourly Rate:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.hourlyRate || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Regular Hours:</span>
                      <span className="font-medium">{selectedStaff.regularHours}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Regular Pay:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.regularPay || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Overtime Hours:</span>
                      <span className="font-medium">{selectedStaff.overtimeHours}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Overtime Pay:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.overtimePay || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Commissions:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.commissions || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Gross Pay:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.grossPay || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Deductions:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.deductions || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Net Pay:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.netPay || 0} /></span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">Additional Costs (Monthly)</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span>Visa Cost:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.visaCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Manpower Agency:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.manpowerCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Medical & Insurance:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.medicalCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>ID Issuance/Renewal:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.idCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accommodation:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.accommodationCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Transportation:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.transportationCost || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Other Costs:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.otherCosts || 0} /></span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Additional Costs:</span>
                      <span className="font-medium"><CurrencyDisplay amount={selectedStaff.totalAdditionalCosts || 0} /></span>
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="font-medium">Total Cost to Company (Monthly):</span>
                    </div>
                    <span className="text-lg font-medium"><CurrencyDisplay amount={selectedStaff.totalCostToCompany || 0} /></span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Annual Cost to Company:</span>
                    <span><CurrencyDisplay amount={(selectedStaff.totalCostToCompany || 0) * 12} /></span>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
                  Close
                </Button>
                <Button variant="default" onClick={() => {
                  // Open the edit cost dialog which allows editing all cost components
                  setIsEditCostDialogOpen(true);
                }}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Costs
                </Button>
                <Button>
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Add Cost Dialog */}
        <Dialog open={isAddCostDialogOpen} onOpenChange={setIsAddCostDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add Staff Cost</DialogTitle>
              <DialogDescription>
                Add a new cost item for a staff member.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="staff">Staff Member</Label>
                <Select
                  value={newCost.staffId}
                  onValueChange={(value) => setNewCost({...newCost, staffId: value})}
                >
                  <SelectTrigger id="staff">
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockStaff.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="costType">Cost Type</Label>
                <Select
                  value={newCost.costType}
                  onValueChange={(value) => setNewCost({...newCost, costType: value})}
                >
                  <SelectTrigger id="costType">
                    <SelectValue placeholder="Select cost type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="visa">Visa</SelectItem>
                    <SelectItem value="manpower">Manpower Agency</SelectItem>
                    <SelectItem value="medical">Medical & Insurance</SelectItem>
                    <SelectItem value="id">ID Issuance/Renewal</SelectItem>
                    <SelectItem value="accommodation">Accommodation</SelectItem>
                    <SelectItem value="transportation">Transportation</SelectItem>
                    <SelectItem value="other">Other Costs</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="amount">Amount</Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={newCost.amount || ""}
                    onChange={(e) => setNewCost({...newCost, amount: parseFloat(e.target.value) || 0})}
                    className="pl-8"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                    {formatCurrency(0).replace(/[0-9.]/g, '')}
                  </div>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={newCost.date}
                  onChange={(e) => setNewCost({...newCost, date: e.target.value})}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={newCost.description}
                  onChange={(e) => setNewCost({...newCost, description: e.target.value})}
                  placeholder="Enter description..."
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddCostDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                // In a real app, this would save the cost to the database
                setIsAddCostDialogOpen(false)
              }}>
                <DollarSign className="mr-2 h-4 w-4" />
                Add Cost
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        {/* Export Dialog */}
        <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Export Payroll Data</DialogTitle>
              <DialogDescription>
                Choose export format and data to include.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <h3 className="font-medium">Export Format</h3>
                <RadioGroup
                  value={exportFormat}
                  onValueChange={setExportFormat}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="excel" id="excel" />
                    <Label htmlFor="excel" className="flex items-center">
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Excel (.xlsx)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pdf" id="pdf" />
                    <Label htmlFor="pdf" className="flex items-center">
                      <FilePdf className="mr-2 h-4 w-4" />
                      PDF (.pdf)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="csv" id="csv" />
                    <Label htmlFor="csv" className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      CSV (.csv)
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Data to Include</h3>
                <RadioGroup
                  value={exportData}
                  onValueChange={setExportData}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="current" id="current" />
                    <Label htmlFor="current">Current view only</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="all" />
                    <Label htmlFor="all">All staff data</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-details"
                  checked={true}
                  disabled
                />
                <Label htmlFor="include-details">Include detailed breakdown</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={exportPayroll}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Process Payment Dialog */}
        <Dialog open={isProcessPaymentDialogOpen} onOpenChange={setIsProcessPaymentDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Process Payment</DialogTitle>
              <DialogDescription>
                {selectedStaff && `Process payment for ${selectedStaff.staffName}`}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {selectedStaff && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">Staff</Label>
                      <p className="font-medium">{selectedStaff.staffName}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Amount</Label>
                      <p className="font-medium"><CurrencyDisplay amount={selectedStaff.netPay} /></p>
                    </div>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="paymentDate">Payment Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="paymentDate"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !paymentDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {paymentDate ? format(paymentDate, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={paymentDate}
                          onSelect={(date) => date && setPaymentDate(date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Select
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                    >
                      <SelectTrigger id="paymentMethod">
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                        <SelectItem value="check">Check</SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="paypal">PayPal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="paymentNote">Note (Optional)</Label>
                    <Textarea
                      id="paymentNote"
                      value={paymentNote}
                      onChange={(e) => setPaymentNote(e.target.value)}
                      placeholder="Add any payment notes here..."
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="sendEmail"
                      checked={sendEmail}
                      onCheckedChange={(checked) => setSendEmail(checked as boolean)}
                    />
                    <Label htmlFor="sendEmail">Send payment notification email</Label>
                  </div>
                </>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsProcessPaymentDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={processPayment}>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Process Payment
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Pay Stub Dialog */}
        <Dialog open={isPayStubDialogOpen} onOpenChange={setIsPayStubDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Pay Stub Preview</DialogTitle>
              <DialogDescription>
                {selectedStaff && `Pay stub for ${selectedStaff.staffName}`}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {selectedStaff && (
                <div className="border rounded-md p-4 space-y-4">
                  <div className="flex justify-between items-center border-b pb-2">
                    <div>
                      <h3 className="font-bold text-lg">Vanity Hub Salon</h3>
                      <p className="text-sm text-muted-foreground">123 Main Street, City, State 12345</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Pay Period: {format(new Date(), "MMM d, yyyy")}</p>
                      <p className="text-sm text-muted-foreground">Pay Stub #: PS-{selectedStaff.id.split('-')[1]}-{format(new Date(), "yyyyMMdd")}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Employee</p>
                      <p className="font-medium">{selectedStaff.staffName}</p>
                      <p className="text-sm">ID: {selectedStaff.staffId}</p>
                      <p className="text-sm capitalize">{selectedStaff.role.replace('_', ' ')}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">Payment Method</p>
                      <p className="font-medium">Direct Deposit</p>
                      <p className="text-sm text-muted-foreground">Payment Date</p>
                      <p className="text-sm">{format(new Date(), "MMM d, yyyy")}</p>
                    </div>
                  </div>

                  <div className="border-t pt-2">
                    <h4 className="font-medium mb-2">Earnings</h4>
                    <div className="grid grid-cols-4 gap-2 text-sm">
                      <div className="font-medium">Description</div>
                      <div className="font-medium text-right">Hours</div>
                      <div className="font-medium text-right">Rate</div>
                      <div className="font-medium text-right">Amount</div>

                      <div>Regular Pay</div>
                      <div className="text-right">{selectedStaff.regularHours}</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.hourlyRate} /></div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.regularPay} /></div>

                      <div>Overtime Pay</div>
                      <div className="text-right">{selectedStaff.overtimeHours}</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.hourlyRate * 1.5} /></div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.overtimePay} /></div>

                      <div>Commissions</div>
                      <div className="text-right">-</div>
                      <div className="text-right">-</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.commissions} /></div>

                      <div className="col-span-3 font-medium">Gross Pay</div>
                      <div className="text-right font-medium"><CurrencyDisplay amount={selectedStaff.grossPay} /></div>
                    </div>
                  </div>

                  <div className="border-t pt-2">
                    <h4 className="font-medium mb-2">Deductions</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">Description</div>
                      <div className="font-medium text-right">Amount</div>

                      <div>Federal Income Tax</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.deductions * 0.6} /></div>

                      <div>Social Security</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.deductions * 0.25} /></div>

                      <div>Medicare</div>
                      <div className="text-right"><CurrencyDisplay amount={selectedStaff.deductions * 0.15} /></div>

                      <div className="font-medium">Total Deductions</div>
                      <div className="text-right font-medium"><CurrencyDisplay amount={selectedStaff.deductions} /></div>
                    </div>
                  </div>

                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <h4 className="font-bold">Net Pay</h4>
                      <p className="font-bold text-lg"><CurrencyDisplay amount={selectedStaff.netPay} /></p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPayStubDialogOpen(false)}>
                Close
              </Button>
              <Button onClick={downloadPayStub}>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
              <Button variant="outline" onClick={() => {
                toast({
                  title: "Email sent",
                  description: `Pay stub has been emailed to ${selectedStaff?.staffName}.`,
                })
              }}>
                <Mail className="mr-2 h-4 w-4" />
                Email
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>



        {/* Edit Cost to Company Dialog */}
        <Dialog open={isEditCostDialogOpen} onOpenChange={setIsEditCostDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Edit Staff Cost Details</DialogTitle>
              <DialogDescription>
                {selectedStaff && `Edit all cost details for ${selectedStaff.staffName}`}
              </DialogDescription>
            </DialogHeader>
            {selectedStaff && (
              <form onSubmit={(e) => {
                e.preventDefault();

                // Get the salary details values
                const hourlyRate = parseFloat(selectedStaff.hourlyRate);
                const regularHours = parseFloat(selectedStaff.regularHours);
                const overtimeHours = parseFloat(selectedStaff.overtimeHours);
                const commissions = parseFloat(selectedStaff.commissions);

                // Calculate salary-related values
                const regularPay = hourlyRate * regularHours;
                const overtimePay = hourlyRate * 1.5 * overtimeHours;
                const grossPay = regularPay + overtimePay + commissions;
                const deductions = grossPay * 0.2; // 20% for taxes and deductions
                const netPay = grossPay - deductions;

                // Get the additional costs values
                const visaCost = parseFloat(selectedStaff.visaCost);
                const manpowerCost = parseFloat(selectedStaff.manpowerCost);
                const medicalCost = parseFloat(selectedStaff.medicalCost);
                const idCost = parseFloat(selectedStaff.idCost);
                const accommodationCost = parseFloat(selectedStaff.accommodationCost);
                const transportationCost = parseFloat(selectedStaff.transportationCost);
                const otherCosts = parseFloat(selectedStaff.otherCosts);

                // Calculate the total additional costs
                const totalAdditionalCosts =
                  visaCost +
                  manpowerCost +
                  medicalCost +
                  idCost +
                  accommodationCost +
                  transportationCost +
                  otherCosts;

                // Calculate the total cost to company
                const totalCostToCompany = grossPay + totalAdditionalCosts;

                // Update the payroll data
                const updatedPayrollData = payrollData.map(item => {
                  if (item.id === selectedStaff.id) {
                    return {
                      ...item,
                      hourlyRate,
                      regularHours,
                      overtimeHours,
                      commissions,
                      regularPay: Number.parseFloat(regularPay.toFixed(2)),
                      overtimePay: Number.parseFloat(overtimePay.toFixed(2)),
                      grossPay: Number.parseFloat(grossPay.toFixed(2)),
                      deductions: Number.parseFloat(deductions.toFixed(2)),
                      netPay: Number.parseFloat(netPay.toFixed(2)),
                    };
                  }
                  return item;
                });

                // Update the cost to company data
                const updatedCostToCompanyData = costToCompanyData.map(item => {
                  if (item.id === selectedStaff.id) {
                    return {
                      ...item,
                      grossPay: Number.parseFloat(grossPay.toFixed(2)),
                      visaCost: Number.parseFloat(visaCost.toFixed(2)),
                      manpowerCost: Number.parseFloat(manpowerCost.toFixed(2)),
                      medicalCost: Number.parseFloat(medicalCost.toFixed(2)),
                      idCost: Number.parseFloat(idCost.toFixed(2)),
                      accommodationCost: Number.parseFloat(accommodationCost.toFixed(2)),
                      transportationCost: Number.parseFloat(transportationCost.toFixed(2)),
                      otherCosts: Number.parseFloat(otherCosts.toFixed(2)),
                      totalAdditionalCosts: Number.parseFloat(totalAdditionalCosts.toFixed(2)),
                      totalCostToCompany: Number.parseFloat(totalCostToCompany.toFixed(2))
                    };
                  }
                  return item;
                });

                // Update the states with the new data
                setPayrollData(updatedPayrollData);
                setCostToCompanyData(updatedCostToCompanyData);

                // Close the dialog
                setIsEditCostDialogOpen(false);

                toast({
                  title: "Staff Cost Details Updated",
                  description: `All cost details for ${selectedStaff.staffName} have been updated successfully.`,
                });
              }}>
                <div className="grid gap-4 py-4">
                  <h3 className="font-medium border-b pb-2">Salary Details</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="hourlyRate">Hourly Rate</Label>
                      <Input
                        id="hourlyRate"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.hourlyRate}
                        onChange={(e) => {
                          selectedStaff.hourlyRate = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="regularHours">Regular Hours</Label>
                      <Input
                        id="regularHours"
                        type="number"
                        min="0"
                        step="1"
                        defaultValue={selectedStaff.regularHours}
                        onChange={(e) => {
                          selectedStaff.regularHours = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="overtimeHours">Overtime Hours</Label>
                      <Input
                        id="overtimeHours"
                        type="number"
                        min="0"
                        step="1"
                        defaultValue={selectedStaff.overtimeHours}
                        onChange={(e) => {
                          selectedStaff.overtimeHours = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="commissions">Commissions</Label>
                      <Input
                        id="commissions"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.commissions}
                        onChange={(e) => {
                          selectedStaff.commissions = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                  </div>

                  <h3 className="font-medium border-b pb-2 mt-4">Additional Costs (Monthly)</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="visaCost">Visa Cost</Label>
                      <Input
                        id="visaCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.visaCost}
                        onChange={(e) => {
                          selectedStaff.visaCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="manpowerCost">Manpower Cost</Label>
                      <Input
                        id="manpowerCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.manpowerCost}
                        onChange={(e) => {
                          selectedStaff.manpowerCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="medicalCost">Medical & Insurance</Label>
                      <Input
                        id="medicalCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.medicalCost}
                        onChange={(e) => {
                          selectedStaff.medicalCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="idCost">ID Issuance/Renewal</Label>
                      <Input
                        id="idCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.idCost}
                        onChange={(e) => {
                          selectedStaff.idCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="accommodationCost">Accommodation</Label>
                      <Input
                        id="accommodationCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.accommodationCost}
                        onChange={(e) => {
                          selectedStaff.accommodationCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="transportationCost">Transportation</Label>
                      <Input
                        id="transportationCost"
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={selectedStaff.transportationCost}
                        onChange={(e) => {
                          selectedStaff.transportationCost = parseFloat(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="otherCosts">Other Costs</Label>
                    <Input
                      id="otherCosts"
                      type="number"
                      min="0"
                      step="0.01"
                      defaultValue={selectedStaff.otherCosts}
                      onChange={(e) => {
                        selectedStaff.otherCosts = parseFloat(e.target.value);
                      }}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsEditCostDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    <Save className="mr-2 h-4 w-4" />
                    Save All Changes
                  </Button>
                </DialogFooter>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

