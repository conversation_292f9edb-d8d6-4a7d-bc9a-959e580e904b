"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { format } from "date-fns"
import {
  Calendar,
  User,
  MapPin,
  CreditCard,
  FileText,
  Package,
  ShoppingCart,
  Globe,
  Edit,
  Printer,
  Download,
  ExternalLink,
  Clock,
  Hash
} from "lucide-react"
import { Transaction, TransactionSource } from "@/lib/transaction-types"
import { useToast } from "@/components/ui/use-toast"

interface TransactionDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transaction: Transaction | null
  onPrintReceipt?: (transaction: Transaction) => void
  onExportPDF?: (transaction: Transaction) => void
  onViewReference?: (transaction: Transaction) => void
}

export function TransactionDetailsDialog({
  open,
  onOpenChange,
  transaction,
  onPrintReceipt,
  onExportPDF,
  onViewReference
}: TransactionDetailsDialogProps) {
  const { toast } = useToast()

  if (!transaction) return null

  // Helper function to get source icon
  const getSourceIcon = (source: TransactionSource) => {
    switch (source) {
      case TransactionSource.POS:
        return <ShoppingCart className="h-4 w-4" />
      case TransactionSource.CALENDAR:
        return <Calendar className="h-4 w-4" />
      case TransactionSource.MANUAL:
        return <Edit className="h-4 w-4" />
      case TransactionSource.INVENTORY:
        return <Package className="h-4 w-4" />
      case TransactionSource.ONLINE:
        return <Globe className="h-4 w-4" />
      case TransactionSource.CLIENT_PORTAL:
        return <Globe className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // Helper function to get source label
  const getSourceLabel = (source: TransactionSource) => {
    switch (source) {
      case TransactionSource.POS:
        return "Point of Sale"
      case TransactionSource.CALENDAR:
        return "Appointment"
      case TransactionSource.MANUAL:
        return "Manual Entry"
      case TransactionSource.INVENTORY:
        return "Inventory"
      case TransactionSource.ONLINE:
        return "Online Store"
      case TransactionSource.CLIENT_PORTAL:
        return "Client Portal"
      default:
        return "System"
    }
  }

  // Helper function to get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "default"
      case "pending":
        return "secondary"
      case "cancelled":
        return "destructive"
      case "refunded":
        return "outline"
      case "partial":
        return "secondary"
      default:
        return "outline"
    }
  }

  // Helper function to get location name
  const getLocationName = (locationId: string) => {
    switch (locationId) {
      case "loc1":
        return "D-Ring Road Salon"
      case "loc2":
        return "Muaither Salon"
      case "loc3":
        return "Medinat Khalifa Salon"
      case "home":
        return "Home Service"
      default:
        return locationId
    }
  }

  // Helper function to format payment method
  const formatPaymentMethod = (method: string) => {
    switch (method) {
      case "credit_card":
        return "Credit Card"
      case "mobile_payment":
        return "Mobile Payment"
      case "bank_transfer":
        return "Bank Transfer"
      case "cash":
        return "Cash"
      default:
        return method.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }

  const handlePrintReceipt = () => {
    if (onPrintReceipt) {
      onPrintReceipt(transaction)
    } else {
      toast({
        title: "Print Receipt",
        description: "Receipt printing functionality will be implemented.",
      })
    }
  }

  const handleExportPDF = () => {
    if (onExportPDF) {
      onExportPDF(transaction)
    } else {
      toast({
        title: "Export PDF",
        description: "PDF export functionality will be implemented.",
      })
    }
  }

  const handleViewReference = () => {
    if (onViewReference) {
      onViewReference(transaction)
    } else {
      toast({
        title: "View Reference",
        description: "Reference viewing functionality will be implemented.",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getSourceIcon(transaction.source)}
            Transaction Details
          </DialogTitle>
          <DialogDescription>
            Complete information for transaction {transaction.id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Transaction Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">{transaction.id}</h3>
              <p className="text-sm text-muted-foreground">
                {typeof transaction.date === 'string' 
                  ? transaction.date 
                  : format(transaction.date, 'PPP p')}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">
                <CurrencyDisplay amount={transaction.amount} />
              </div>
              <Badge variant={getStatusVariant(transaction.status)}>
                {transaction.status}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Transaction Info Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {getSourceIcon(transaction.source)}
                <div>
                  <p className="text-sm font-medium">Source</p>
                  <p className="text-sm text-muted-foreground">
                    {getSourceLabel(transaction.source)}
                  </p>
                </div>
              </div>

              {transaction.clientName && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <div>
                    <p className="text-sm font-medium">Client</p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.clientName}
                    </p>
                  </div>
                </div>
              )}

              {transaction.staffName && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <div>
                    <p className="text-sm font-medium">Staff</p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.staffName}
                    </p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                <div>
                  <p className="text-sm font-medium">Payment Method</p>
                  <p className="text-sm text-muted-foreground">
                    {formatPaymentMethod(transaction.paymentMethod)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <div>
                  <p className="text-sm font-medium">Location</p>
                  <p className="text-sm text-muted-foreground">
                    {getLocationName(transaction.location)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <div>
                  <p className="text-sm font-medium">Type</p>
                  <p className="text-sm text-muted-foreground">
                    {transaction.type}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Description */}
          <div>
            <h4 className="text-sm font-medium mb-2">Description</h4>
            <p className="text-sm text-muted-foreground">
              {transaction.description}
            </p>
          </div>

          {/* Items */}
          {transaction.items && transaction.items.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-3">Items</h4>
                <div className="space-y-2">
                  {transaction.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                      <div>
                        <p className="text-sm font-medium">{item.name}</p>
                        <p className="text-xs text-muted-foreground">
                          Qty: {item.quantity} × <CurrencyDisplay amount={item.unitPrice} />
                        </p>
                      </div>
                      <div className="text-sm font-medium">
                        <CurrencyDisplay amount={item.totalPrice} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Metadata */}
          {transaction.metadata && (
            <>
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-2">Additional Information</h4>
                <div className="text-xs text-muted-foreground space-y-1">
                  {transaction.metadata.appointmentId && (
                    <p>Appointment ID: {transaction.metadata.appointmentId}</p>
                  )}
                  {transaction.metadata.bookingReference && (
                    <p>Booking Reference: {transaction.metadata.bookingReference}</p>
                  )}
                  {transaction.metadata.completedAt && (
                    <p>Completed: {format(new Date(transaction.metadata.completedAt), 'PPp')}</p>
                  )}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={handlePrintReceipt} variant="outline" size="sm">
              <Printer className="mr-2 h-4 w-4" />
              Print Receipt
            </Button>
            <Button onClick={handleExportPDF} variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
            {transaction.reference && (
              <Button onClick={handleViewReference} variant="outline" size="sm">
                <ExternalLink className="mr-2 h-4 w-4" />
                View {transaction.source === TransactionSource.CALENDAR ? 'Appointment' : 
                      transaction.source === TransactionSource.CLIENT_PORTAL ? 'Order' : 'Reference'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
