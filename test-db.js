const { PrismaClient } = require('@prisma/client')

// Load environment variables
require('dotenv').config()

console.log('DATABASE_URL:', process.env.DATABASE_URL)

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function testDatabase() {
  try {
    console.log('🔄 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connected successfully')
    
    // Test services query
    console.log('🔄 Fetching services...')
    const services = await prisma.service.findMany({
      where: {
        isActive: true
      },
      include: {
        locations: {
          where: {
            isActive: true
          },
          include: {
            location: true
          }
        }
      },
      take: 5 // Just get first 5 for testing
    })
    
    console.log(`✅ Found ${services.length} services`)
    services.forEach(service => {
      console.log(`  - ${service.name} (${service.category}) - $${service.price}`)
    })
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
    console.error('Error details:', error.message)
    if (error.code) {
      console.error('Error code:', error.code)
    }
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
