"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useProducts } from "@/lib/product-provider"
import { beautyProducts, defaultProductCategories, defaultProductTypes } from "@/lib/products-data"

export function ProductDebugPanel() {
  const { products, categories, productTypes, getRetailProducts, fixRetailProducts } = useProducts()
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(false)

  const runDebug = () => {
    console.log("🔍 === COMPREHENSIVE PRODUCT DEBUG ===")

    // Get localStorage data
    const storedProducts = typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('salon_products') || '[]')
      : []

    const storedCategories = typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('salon_product_categories') || '[]')
      : []

    // Get retail products
    const retailProducts = getRetailProducts()

    // Analyze Extensions and Nail Care specifically
    const extensionsProducts = products.filter(p => p.category === "Extensions")
    const nailCareProducts = products.filter(p => p.category === "Nail Care")

    const extensionsRetail = extensionsProducts.filter(p => p.isRetail && p.isActive !== false)
    const nailCareRetail = nailCareProducts.filter(p => p.isRetail && p.isActive !== false)

    // Check category names in stored products
    const uniqueCategories = [...new Set(products.map(p => p.category))]

    const info = {
      totalProducts: products.length,
      defaultProducts: beautyProducts.length,
      storedProducts: storedProducts.length,
      retailProducts: retailProducts.length,

      categories: {
        total: categories.length,
        default: defaultProductCategories.length,
        stored: storedCategories.length,
        unique: uniqueCategories
      },

      extensions: {
        total: extensionsProducts.length,
        retail: extensionsRetail.length,
        products: extensionsProducts.map(p => ({
          name: p.name,
          isRetail: p.isRetail,
          isActive: p.isActive,
          category: p.category,
          type: p.type
        }))
      },

      nailCare: {
        total: nailCareProducts.length,
        retail: nailCareRetail.length,
        products: nailCareProducts.map(p => ({
          name: p.name,
          isRetail: p.isRetail,
          isActive: p.isActive,
          category: p.category,
          type: p.type
        }))
      },

      productsByCategory: uniqueCategories.map(cat => ({
        category: cat,
        total: products.filter(p => p.category === cat).length,
        retail: products.filter(p => p.category === cat && p.isRetail && p.isActive !== false).length
      }))
    }

    console.log("📊 Debug Info:", info)
    setDebugInfo(info)
  }

  const fixRetailStatus = () => {
    console.log("🔧 Fixing retail status using product provider...")

    const wasFixed = fixRetailProducts()
    if (wasFixed) {
      console.log("✅ Products fixed! Refreshing debug info...")
      setTimeout(() => {
        runDebug()
      }, 100)
    } else {
      console.log("ℹ️ No products needed fixing")
    }
  }

  const addTestProducts = () => {
    console.log("🧪 Adding test products to Extensions and Nail Care...")

    const testProducts = [
      {
        id: `test-ext-${Date.now()}`,
        name: "Premium Human Hair Extensions",
        description: "High-quality human hair extensions for professional use",
        price: 150,
        category: "Extensions",
        type: "Human Hair",
        image: "https://images.unsplash.com/photo-**********-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        stock: 10,
        minStock: 2,
        sku: "EXT-001",
        isRetail: true,
        isActive: true,
        location: "Main Location",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: `test-nail-${Date.now()}`,
        name: "Professional Nail Polish Set",
        description: "Complete set of professional nail polish colors",
        price: 45,
        category: "Nail Care",
        type: "Nail Polish",
        image: "https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        stock: 25,
        minStock: 5,
        sku: "NAIL-001",
        isRetail: true,
        isActive: true,
        location: "Main Location",
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    const storedProducts = typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('salon_products') || '[]')
      : []

    const updatedProducts = [...storedProducts, ...testProducts]
    localStorage.setItem('salon_products', JSON.stringify(updatedProducts))

    console.log("✅ Test products added! Refreshing...")
    window.location.reload()
  }

  const clearAndReset = () => {
    if (confirm("This will clear all stored product data. Are you sure?")) {
      localStorage.removeItem('salon_products')
      localStorage.removeItem('salon_product_categories')
      localStorage.removeItem('salon_product_types')
      localStorage.removeItem('salon_product_transfers')
      console.log("🧹 All product data cleared")
      window.location.reload()
    }
  }

  useEffect(() => {
    runDebug()
  }, [products, categories])

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-red-100 border-red-300 text-red-700 hover:bg-red-200"
        >
          🔍 Debug Products
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed inset-4 z-50 bg-white border-2 border-red-300 rounded-lg shadow-lg overflow-auto">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-red-700">🔍 Product Debug Panel</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="outline"
              size="sm"
            >
              ✕ Close
            </Button>
          </div>
          <CardDescription>
            Debugging product visibility issues for Extensions and Nail Care
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button onClick={runDebug} variant="outline" size="sm">
              🔄 Refresh Debug
            </Button>
            <Button onClick={fixRetailStatus} variant="outline" size="sm" className="bg-green-50">
              🔧 Fix Retail Status
            </Button>
            <Button onClick={addTestProducts} variant="outline" size="sm" className="bg-blue-50">
              🧪 Add Test Products
            </Button>
            <Button onClick={clearAndReset} variant="outline" size="sm" className="bg-red-50">
              🧹 Clear All Data
            </Button>
          </div>

          {debugInfo && (
            <div className="space-y-4 text-sm">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold">Product Counts</h4>
                  <ul className="space-y-1">
                    <li>Total Products: {debugInfo.totalProducts}</li>
                    <li>Default Products: {debugInfo.defaultProducts}</li>
                    <li>Stored Products: {debugInfo.storedProducts}</li>
                    <li>Retail Products: {debugInfo.retailProducts}</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold">Categories</h4>
                  <ul className="space-y-1">
                    <li>Total Categories: {debugInfo.categories.total}</li>
                    <li>Default Categories: {debugInfo.categories.default}</li>
                    <li>Stored Categories: {debugInfo.categories.stored}</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="font-semibold">Extensions Products ({debugInfo.extensions.total} total, {debugInfo.extensions.retail} retail)</h4>
                {debugInfo.extensions.products.length > 0 ? (
                  <ul className="space-y-1 max-h-32 overflow-y-auto">
                    {debugInfo.extensions.products.map((p: any, i: number) => (
                      <li key={i} className={`text-xs ${p.isRetail && p.isActive !== false ? 'text-green-600' : 'text-red-600'}`}>
                        {p.name} - Retail: {String(p.isRetail)}, Active: {String(p.isActive)}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-red-600 text-xs">No Extensions products found</p>
                )}
              </div>

              <div>
                <h4 className="font-semibold">Nail Care Products ({debugInfo.nailCare.total} total, {debugInfo.nailCare.retail} retail)</h4>
                {debugInfo.nailCare.products.length > 0 ? (
                  <ul className="space-y-1 max-h-32 overflow-y-auto">
                    {debugInfo.nailCare.products.map((p: any, i: number) => (
                      <li key={i} className={`text-xs ${p.isRetail && p.isActive !== false ? 'text-green-600' : 'text-red-600'}`}>
                        {p.name} - Retail: {String(p.isRetail)}, Active: {String(p.isActive)}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-red-600 text-xs">No Nail Care products found</p>
                )}
              </div>

              <div>
                <h4 className="font-semibold">All Categories</h4>
                <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                  {debugInfo.productsByCategory.map((cat: any, i: number) => (
                    <div key={i} className="text-xs">
                      <strong>{cat.category}:</strong> {cat.total} total, {cat.retail} retail
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
