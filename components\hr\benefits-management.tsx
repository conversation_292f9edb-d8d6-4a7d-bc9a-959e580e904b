"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Eye, MoreHorizontal, Plus } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useStaff } from "@/lib/staff-provider"
import { useToast } from "@/components/ui/use-toast"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"

// Mock benefits data
const mockBenefitPlans = [
  {
    id: "BP-001",
    name: "Premium Health Plan",
    type: "Health Insurance",
    provider: "Blue Cross",
    coverage: "Comprehensive",
    monthlyCost: 350,
    employerContribution: 80,
    eligibility: "Full-time",
    enrollmentPeriod: "Jan 1 - Jan 31",
  },
  {
    id: "BP-002",
    name: "Standard Health Plan",
    type: "Health Insurance",
    provider: "Blue Cross",
    coverage: "Standard",
    monthlyCost: 250,
    employerContribution: 70,
    eligibility: "Full-time",
    enrollmentPeriod: "Jan 1 - Jan 31",
  },
  {
    id: "BP-003",
    name: "Basic Health Plan",
    type: "Health Insurance",
    provider: "Blue Cross",
    coverage: "Basic",
    monthlyCost: 150,
    employerContribution: 60,
    eligibility: "Part-time (20+ hrs)",
    enrollmentPeriod: "Jan 1 - Jan 31",
  },
  {
    id: "BP-004",
    name: "Dental Plus",
    type: "Dental Insurance",
    provider: "Delta Dental",
    coverage: "Comprehensive",
    monthlyCost: 75,
    employerContribution: 50,
    eligibility: "Full-time",
    enrollmentPeriod: "Jan 1 - Jan 31",
  },
  {
    id: "BP-005",
    name: "Vision Care",
    type: "Vision Insurance",
    provider: "VSP",
    coverage: "Standard",
    monthlyCost: 45,
    employerContribution: 50,
    eligibility: "Full-time",
    enrollmentPeriod: "Jan 1 - Jan 31",
  },
  {
    id: "BP-006",
    name: "401(k) Retirement",
    type: "Retirement",
    provider: "Fidelity",
    coverage: "Matching up to 4%",
    monthlyCost: 0,
    employerContribution: 100,
    eligibility: "Full-time (after 90 days)",
    enrollmentPeriod: "Anytime",
  },
]

// Mock staff enrollments - UPDATED TO USE REAL STAFF DATA
const mockEnrollments = [
  {
    id: "EN-001",
    staffId: "staff-real-1",
    staffName: "Mekdes Abebe",
    benefitPlanId: "BP-001",
    benefitPlanName: "Premium Health Plan",
    startDate: "2025-01-01",
    status: "Active",
    dependents: 2,
    monthlyCost: 350,
    employeeContribution: 70,
    employerContribution: 280,
  },
  {
    id: "EN-002",
    staffId: "staff-real-1",
    staffName: "Mekdes Abebe",
    benefitPlanId: "BP-004",
    benefitPlanName: "Dental Plus",
    startDate: "2025-01-01",
    status: "Active",
    dependents: 2,
    monthlyCost: 75,
    employeeContribution: 37.5,
    employerContribution: 37.5,
  },
  {
    id: "EN-003",
    staffId: "staff-real-2",
    staffName: "Woyni Tade",
    benefitPlanId: "BP-002",
    benefitPlanName: "Standard Health Plan",
    startDate: "2025-01-01",
    status: "Active",
    dependents: 0,
    monthlyCost: 250,
    employeeContribution: 75,
    employerContribution: 175,
  },
  {
    id: "EN-004",
    staffId: "staff-real-3",
    staffName: "Maria Santos",
    benefitPlanId: "BP-003",
    benefitPlanName: "Basic Health Plan",
    startDate: "2025-01-01",
    status: "Active",
    dependents: 1,
    monthlyCost: 150,
    employeeContribution: 60,
    employerContribution: 90,
  },
  {
    id: "EN-005",
    staffId: "staff-real-4",
    staffName: "Fatima Al-Zahra",
    benefitPlanId: "BP-001",
    benefitPlanName: "Premium Health Plan",
    startDate: "2025-01-01",
    status: "Active",
    dependents: 3,
    monthlyCost: 350,
    employeeContribution: 70,
    employerContribution: 280,
  },
]

export function BenefitsManagement() {
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const { staff: realStaff } = useStaff()
  const [benefitPlans, setBenefitPlans] = useState(mockBenefitPlans)
  const [enrollments, setEnrollments] = useState(mockEnrollments)
  const [isNewEnrollmentDialogOpen, setIsNewEnrollmentDialogOpen] = useState(false)
  const [isViewEnrollmentDialogOpen, setIsViewEnrollmentDialogOpen] = useState(false)
  const [selectedEnrollment, setSelectedEnrollment] = useState<any>(null)
  const [newEnrollment, setNewEnrollment] = useState({
    staffId: "",
    benefitPlanId: "",
    dependents: 0,
  })

  const handleCreateEnrollment = () => {
    const staff = realStaff.find(s => s.id === newEnrollment.staffId)
    const plan = benefitPlans.find(p => p.id === newEnrollment.benefitPlanId)

    if (!staff || !plan) return

    // Check if staff is already enrolled in this plan
    const existingEnrollment = enrollments.find(
      e => e.staffId === newEnrollment.staffId && e.benefitPlanId === newEnrollment.benefitPlanId
    )

    if (existingEnrollment) {
      toast({
        title: "Enrollment failed",
        description: `${staff.name} is already enrolled in ${plan.name}.`,
        variant: "destructive",
      })
      return
    }

    const employeeContribution = plan.monthlyCost * (1 - (plan.employerContribution / 100))
    const employerContribution = plan.monthlyCost * (plan.employerContribution / 100)

    const newEnrollmentEntry = {
      id: `EN-${enrollments.length + 1}`.padStart(6, '0'),
      staffId: newEnrollment.staffId,
      staffName: staff.name,
      benefitPlanId: newEnrollment.benefitPlanId,
      benefitPlanName: plan.name,
      startDate: new Date().toISOString().split('T')[0],
      status: "Active",
      dependents: newEnrollment.dependents,
      monthlyCost: plan.monthlyCost,
      employeeContribution: employeeContribution,
      employerContribution: employerContribution,
    }

    setEnrollments([...enrollments, newEnrollmentEntry])
    setIsNewEnrollmentDialogOpen(false)

    toast({
      title: "Enrollment created",
      description: `${staff.name} has been enrolled in ${plan.name}.`,
    })
  }

  const handleViewEnrollment = (enrollment: any) => {
    setSelectedEnrollment(enrollment)
    setIsViewEnrollmentDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Benefits Management</h3>
        <Button onClick={() => setIsNewEnrollmentDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Enrollment
        </Button>
      </div>

      <Tabs defaultValue="enrollments" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="enrollments">Enrollments</TabsTrigger>
          <TabsTrigger value="plans">Benefit Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="enrollments">
          <Card>
            <CardHeader>
              <CardTitle>Staff Benefit Enrollments</CardTitle>
              <CardDescription>
                View and manage staff benefit enrollments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Staff</TableHead>
                      <TableHead>Benefit Plan</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>Dependents</TableHead>
                      <TableHead className="text-right">Monthly Cost</TableHead>
                      <TableHead className="text-right">Employee Pays</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {enrollments.map((enrollment) => (
                      <TableRow key={enrollment.id}>
                        <TableCell className="font-medium">{enrollment.staffName}</TableCell>
                        <TableCell>{enrollment.benefitPlanName}</TableCell>
                        <TableCell>{enrollment.startDate}</TableCell>
                        <TableCell>{enrollment.dependents}</TableCell>
                        <TableCell className="text-right"><CurrencyDisplay amount={enrollment.monthlyCost} /></TableCell>
                        <TableCell className="text-right"><CurrencyDisplay amount={enrollment.employeeContribution} /></TableCell>
                        <TableCell>
                          <Badge variant={enrollment.status === "Active" ? "success" : "outline"}>
                            {enrollment.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewEnrollment(enrollment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View details
                              </DropdownMenuItem>
                              <DropdownMenuItem>Edit enrollment</DropdownMenuItem>
                              <DropdownMenuItem>Terminate enrollment</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <CardTitle>Available Benefit Plans</CardTitle>
              <CardDescription>
                View and manage benefit plans offered to staff
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Coverage</TableHead>
                      <TableHead className="text-right">Monthly Cost</TableHead>
                      <TableHead className="text-right">Employer Contribution</TableHead>
                      <TableHead>Eligibility</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {benefitPlans.map((plan) => (
                      <TableRow key={plan.id}>
                        <TableCell className="font-medium">{plan.name}</TableCell>
                        <TableCell>{plan.type}</TableCell>
                        <TableCell>{plan.provider}</TableCell>
                        <TableCell>{plan.coverage}</TableCell>
                        <TableCell className="text-right"><CurrencyDisplay amount={plan.monthlyCost} /></TableCell>
                        <TableCell className="text-right">{plan.employerContribution}%</TableCell>
                        <TableCell>{plan.eligibility}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View details
                              </DropdownMenuItem>
                              <DropdownMenuItem>Edit plan</DropdownMenuItem>
                              <DropdownMenuItem>Manage enrollments</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* New Enrollment Dialog */}
      <Dialog open={isNewEnrollmentDialogOpen} onOpenChange={setIsNewEnrollmentDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Enrollment</DialogTitle>
            <DialogDescription>
              Enroll a staff member in a benefit plan.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="staff">Staff Member</Label>
              <Select
                value={newEnrollment.staffId}
                onValueChange={(value) => setNewEnrollment({...newEnrollment, staffId: value})}
              >
                <SelectTrigger id="staff">
                  <SelectValue placeholder="Select staff member" />
                </SelectTrigger>
                <SelectContent>
                  {realStaff.map((staff) => (
                    <SelectItem key={staff.id} value={staff.id}>
                      {staff.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="plan">Benefit Plan</Label>
              <Select
                value={newEnrollment.benefitPlanId}
                onValueChange={(value) => setNewEnrollment({...newEnrollment, benefitPlanId: value})}
              >
                <SelectTrigger id="plan">
                  <SelectValue placeholder="Select benefit plan" />
                </SelectTrigger>
                <SelectContent>
                  {benefitPlans.map((plan) => (
                    <SelectItem key={plan.id} value={plan.id}>
                      {plan.name} ({plan.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="dependents">Number of Dependents</Label>
              <Input
                id="dependents"
                type="number"
                min="0"
                value={newEnrollment.dependents}
                onChange={(e) => setNewEnrollment({...newEnrollment, dependents: parseInt(e.target.value) || 0})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNewEnrollmentDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateEnrollment}>Create Enrollment</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Enrollment Dialog */}
      {selectedEnrollment && (
        <Dialog open={isViewEnrollmentDialogOpen} onOpenChange={setIsViewEnrollmentDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Enrollment Details</DialogTitle>
              <DialogDescription>
                Enrollment #{selectedEnrollment.id}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Staff</Label>
                  <p className="font-medium">{selectedEnrollment.staffName}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Status</Label>
                  <p>
                    <Badge variant={selectedEnrollment.status === "Active" ? "success" : "outline"}>
                      {selectedEnrollment.status}
                    </Badge>
                  </p>
                </div>
              </div>
              <div>
                <Label className="text-muted-foreground">Benefit Plan</Label>
                <p className="font-medium">{selectedEnrollment.benefitPlanName}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Start Date</Label>
                  <p>{selectedEnrollment.startDate}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Dependents</Label>
                  <p>{selectedEnrollment.dependents}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Monthly Cost</Label>
                  <p className="font-medium"><CurrencyDisplay amount={selectedEnrollment.monthlyCost} /></p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Employee Pays</Label>
                  <p className="font-medium"><CurrencyDisplay amount={selectedEnrollment.employeeContribution} /></p>
                </div>
              </div>
              <div>
                <Label className="text-muted-foreground">Employer Contribution</Label>
                <p className="font-medium"><CurrencyDisplay amount={selectedEnrollment.employerContribution} /></p>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setIsViewEnrollmentDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
