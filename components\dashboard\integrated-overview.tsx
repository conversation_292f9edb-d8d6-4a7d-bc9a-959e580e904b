"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useAuth } from "@/lib/auth-provider"
import { integratedAnalyticsService, type IntegratedAnalytics } from "@/lib/integrated-analytics-service"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  AlertTriangle,
  Target,
  BarChart3,
  PieChart
} from "lucide-react"

interface IntegratedOverviewProps {
  dateRange?: {
    from: Date;
    to: Date;
  };
}

function IntegratedOverview({ dateRange }: IntegratedOverviewProps) {
  const { currentLocation } = useAuth()
  const [analytics, setAnalytics] = useState<IntegratedAnalytics | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadAnalytics = () => {
      try {
        const data = integratedAnalyticsService.getAnalytics(
          dateRange?.from,
          dateRange?.to,
          currentLocation?.id?.toString()
        )
        setAnalytics(data)
      } catch (error) {
        console.error('Failed to load integrated analytics:', error)
      } finally {
        setLoading(false)
      }
    }

    loadAnalytics()

    // Subscribe to analytics updates
    const unsubscribe = integratedAnalyticsService.subscribe(setAnalytics)
    return unsubscribe
  }, [dateRange, currentLocation])

  if (loading || !analytics) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-muted rounded w-full"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const getStatusColor = (value: number, threshold: number, inverse = false) => {
    const isGood = inverse ? value < threshold : value > threshold
    return isGood ? "text-green-600" : "text-red-600"
  }

  const getStatusIcon = (value: number, threshold: number, inverse = false) => {
    const isGood = inverse ? value < threshold : value > threshold
    return isGood ? TrendingUp : TrendingDown
  }

  return (
    <div className="space-y-6">
      {/* Key Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <CurrencyDisplay amount={analytics.totalRevenue} />
            </div>
            <div className="flex items-center space-x-2 text-xs mt-2">
              <div className="flex items-center">
                {React.createElement(getStatusIcon(analytics.revenueGrowth, 0), {
                  className: `h-3 w-3 ${getStatusColor(analytics.revenueGrowth, 0)}`
                })}
                <span className={getStatusColor(analytics.revenueGrowth, 0)}>
                  {analytics.revenueGrowth >= 0 ? '+' : ''}{analytics.revenueGrowth.toFixed(1)}%
                </span>
              </div>
              <span className="text-muted-foreground">vs last period</span>
            </div>
            <div className="mt-2 text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Services:</span>
                <span><CurrencyDisplay amount={analytics.serviceRevenue} /></span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Products:</span>
                <span><CurrencyDisplay amount={analytics.productRevenue} /></span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <CurrencyDisplay amount={analytics.netProfit} />
            </div>
            <div className="flex items-center space-x-2 text-xs mt-2">
              <span className={getStatusColor(analytics.netMargin, 20)}>
                {analytics.netMargin.toFixed(1)}% margin
              </span>
            </div>
            <div className="mt-2 text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Gross Profit:</span>
                <span><CurrencyDisplay amount={analytics.grossProfit} /></span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Gross Margin:</span>
                <span>{analytics.grossMargin.toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <CurrencyDisplay amount={analytics.inventoryValue} />
            </div>
            <div className="flex items-center space-x-2 text-xs mt-2">
              <span className={getStatusColor(analytics.inventoryTurnover, 5)}>
                {analytics.inventoryTurnover.toFixed(1)}x turnover
              </span>
            </div>
            <div className="mt-2 text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Low Stock:</span>
                <span className={analytics.lowStockItems > 0 ? "text-yellow-600" : ""}>
                  {analytics.lowStockItems} items
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Out of Stock:</span>
                <span className={analytics.outOfStockItems > 0 ? "text-red-600" : ""}>
                  {analytics.outOfStockItems} items
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Transaction</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <CurrencyDisplay amount={analytics.averageTransactionValue} />
            </div>
            <div className="flex items-center space-x-2 text-xs mt-2">
              <span className={getStatusColor(analytics.profitPerTransaction, 20)}>
                <CurrencyDisplay amount={analytics.profitPerTransaction} /> profit
              </span>
            </div>
            <div className="mt-2 text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Per Client:</span>
                <span><CurrencyDisplay amount={analytics.revenuePerClient} /></span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Cash Flow:</span>
                <span className={getStatusColor(analytics.cashFlow, 0)}>
                  <CurrencyDisplay amount={analytics.cashFlow} />
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Top Selling Products
            </CardTitle>
            <CardDescription>Best performing products by revenue</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topSellingProducts.slice(0, 5).map((product, index) => (
                <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <span className="font-medium text-sm">{product.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {product.quantitySold} units • {product.margin.toFixed(1)}% margin
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-sm">
                      <CurrencyDisplay amount={product.revenue} />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      <CurrencyDisplay amount={product.profit} /> profit
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Top Services
            </CardTitle>
            <CardDescription>Most popular services by bookings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.topServices.slice(0, 5).map((service, index) => (
                <div key={service.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <span className="font-medium text-sm">{service.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {service.bookings} bookings • <CurrencyDisplay amount={service.averagePrice} /> avg
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-sm">
                      <CurrencyDisplay amount={service.revenue} />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Total revenue
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Recommendations */}
      {(analytics.lowStockItems > 0 || analytics.outOfStockItems > 0 || analytics.netMargin < 20) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Alerts & Recommendations
            </CardTitle>
            <CardDescription>Items requiring your attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.lowStockItems > 0 && (
                <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <div>
                    <div className="font-medium text-sm">Low Stock Alert</div>
                    <div className="text-xs text-muted-foreground">
                      {analytics.lowStockItems} products are running low on inventory
                    </div>
                  </div>
                </div>
              )}

              {analytics.outOfStockItems > 0 && (
                <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <div>
                    <div className="font-medium text-sm">Out of Stock</div>
                    <div className="text-xs text-muted-foreground">
                      {analytics.outOfStockItems} products are completely out of stock
                    </div>
                  </div>
                </div>
              )}

              {analytics.netMargin < 20 && (
                <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <Target className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="font-medium text-sm">Profit Margin Opportunity</div>
                    <div className="text-xs text-muted-foreground">
                      Current margin is {analytics.netMargin.toFixed(1)}%. Consider optimizing pricing or reducing costs.
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default IntegratedOverview
