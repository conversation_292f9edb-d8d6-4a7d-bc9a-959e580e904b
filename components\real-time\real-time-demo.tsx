"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useNotification } from '@/components/real-time/real-time-notifications'
import { useRealTimeUpdates, useDataRefresh } from '@/hooks/use-real-time-updates'
import { RealTimeEventType } from '@/lib/real-time-service'
import { useTransactions } from '@/lib/transaction-provider'
import { TransactionSource, TransactionType, TransactionStatus, PaymentMethod } from '@/lib/transaction-types'
import { addAppointment } from '@/lib/appointment-service'
import type { AppointmentStatus } from '@/lib/types/appointment'

export function RealTimeDemo() {
  const [eventCount, setEventCount] = useState(0)
  const { showSuccess, showInfo, showWarning } = useNotification()
  const { addTransaction } = useTransactions()
  const { refreshAll, refreshTransactions, refreshAppointments } = useDataRefresh()

  // Set up real-time event listeners
  const { emitEvent, isConnected, lastEventTime } = useRealTimeUpdates({
    [RealTimeEventType.TRANSACTION_CREATED]: (event) => {
      setEventCount(prev => prev + 1)
      showSuccess(
        'New Transaction!',
        `Transaction created: ${event.payload.clientName} - $${event.payload.amount}`,
        { duration: 3000 }
      )
    },
    [RealTimeEventType.APPOINTMENT_CREATED]: (event) => {
      setEventCount(prev => prev + 1)
      showInfo(
        'New Appointment!',
        `Appointment booked: ${event.payload.clientName} with ${event.payload.staffName}`,
        { duration: 3000 }
      )
    },
    [RealTimeEventType.DATA_REFRESH_REQUESTED]: (event) => {
      showInfo(
        'Data Refresh',
        `Data refresh requested: ${event.payload.target || 'all'}`,
        { duration: 2000 }
      )
    }
  })

  const createTestTransaction = () => {
    const testTransaction = {
      date: new Date(),
      clientName: `Test Client ${Date.now()}`,
      type: TransactionType.INCOME,
      category: 'Service',
      description: 'Test Service - Real-time Demo',
      amount: Math.floor(Math.random() * 200) + 50,
      paymentMethod: PaymentMethod.CREDIT_CARD,
      status: TransactionStatus.COMPLETED,
      location: 'loc1',
      source: TransactionSource.POS
    }

    addTransaction(testTransaction)
    showSuccess('Transaction Created', 'Test transaction added successfully!')
  }

  const createTestAppointment = () => {
    const testAppointment = {
      id: `apt-${Date.now()}`,
      clientId: `client-${Date.now()}`,
      clientName: `Test Client ${Date.now()}`,
      staffId: 'staff1',
      staffName: 'Test Staff',
      service: 'Test Service',
      date: new Date().toISOString(),
      duration: 60,
      location: 'loc1',
      price: 100,
      status: 'confirmed' as AppointmentStatus,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    addAppointment(testAppointment)
    showSuccess('Appointment Created', 'Test appointment added successfully!')
  }

  const triggerCustomEvent = () => {
    emitEvent(RealTimeEventType.DATA_REFRESH_REQUESTED, {
      source: 'demo',
      target: 'custom-test',
      timestamp: new Date().toISOString()
    })
  }

  const showTestNotifications = () => {
    showSuccess('Success!', 'This is a success notification')
    setTimeout(() => {
      showWarning('Warning!', 'This is a warning notification')
    }, 1000)
    setTimeout(() => {
      showInfo('Info', 'This is an info notification')
    }, 2000)
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Real-Time System Demo</CardTitle>
        <div className="text-sm text-muted-foreground">
          <div>Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}</div>
          <div>Events Received: {eventCount}</div>
          <div>Last Event: {lastEventTime ? lastEventTime.toLocaleTimeString() : 'None'}</div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Button onClick={createTestTransaction} variant="default">
            Create Test Transaction
          </Button>
          <Button onClick={createTestAppointment} variant="default">
            Create Test Appointment
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <Button onClick={triggerCustomEvent} variant="outline">
            Trigger Custom Event
          </Button>
          <Button onClick={showTestNotifications} variant="outline">
            Show Test Notifications
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-2">
          <Button onClick={refreshAll} variant="secondary" size="sm">
            Refresh All
          </Button>
          <Button onClick={refreshTransactions} variant="secondary" size="sm">
            Refresh Transactions
          </Button>
          <Button onClick={refreshAppointments} variant="secondary" size="sm">
            Refresh Appointments
          </Button>
        </div>

        <div className="text-xs text-muted-foreground bg-gray-50 p-3 rounded">
          <strong>How it works:</strong>
          <ul className="mt-2 space-y-1">
            <li>• Create transactions/appointments to see real-time events</li>
            <li>• Events are broadcast across all components automatically</li>
            <li>• Dashboard cards update instantly without page refresh</li>
            <li>• Notifications appear in real-time</li>
            <li>• Cross-tab communication works between browser tabs</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
