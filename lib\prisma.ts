// Temporary fallback to mock data until Prisma client issue is resolved
console.log('🚨 Using mock data fallback due to Prisma client configuration issue')

// Mock service data
const mockServices = [
  {
    id: "1",
    name: "Hair Cut & Style",
    description: "Professional hair cutting and styling",
    duration: 60,
    price: 50,
    category: "Styling",
    categoryName: "Styling",
    locations: ["1", "2"],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "2",
    name: "Hair Color",
    description: "Full hair coloring service",
    duration: 120,
    price: 80,
    category: "Color",
    categoryName: "Color",
    locations: ["1", "2"],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "3",
    name: "Manicure",
    description: "Professional nail care and polish",
    duration: 45,
    price: 30,
    category: "Nail",
    categoryName: "Nail",
    locations: ["1", "2"],
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

// Mock Prisma client
export const prisma = {
  service: {
    findMany: async (options?: any) => {
      console.log('🔄 Mock: Fetching services...')
      return mockServices
    },
    findUnique: async (options: any) => {
      console.log('🔄 Mock: Fetching service by ID:', options.where.id)
      return mockServices.find(s => s.id === options.where.id) || null
    },
    create: async (options: any) => {
      console.log('🔄 Mock: Creating service:', options.data.name)
      const newService = {
        id: String(mockServices.length + 1),
        ...options.data,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      mockServices.push(newService)
      return newService
    },
    update: async (options: any) => {
      console.log('🔄 Mock: Updating service:', options.where.id)
      const index = mockServices.findIndex(s => s.id === options.where.id)
      if (index !== -1) {
        mockServices[index] = { ...mockServices[index], ...options.data, updatedAt: new Date() }
        return mockServices[index]
      }
      throw new Error('Service not found')
    },
    delete: async (options: any) => {
      console.log('🔄 Mock: Deleting service:', options.where.id)
      const index = mockServices.findIndex(s => s.id === options.where.id)
      if (index !== -1) {
        const deleted = mockServices[index]
        mockServices.splice(index, 1)
        return deleted
      }
      throw new Error('Service not found')
    }
  },
  location: {
    findMany: async () => {
      console.log('🔄 Mock: Fetching locations...')
      return [
        { id: "1", name: "D-ring road", isActive: true },
        { id: "2", name: "Muaither", isActive: true }
      ]
    }
  },
  product: {
    findMany: async () => {
      console.log('🔄 Mock: Fetching products...')
      return []
    }
  },
  staffMember: {
    findMany: async () => {
      console.log('🔄 Mock: Fetching staff...')
      return []
    }
  }
}
