"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Trash2 } from "lucide-react"

export default function ResetTransactionsPage() {
  const { toast } = useToast()
  const [isResetting, setIsResetting] = useState(false)

  const resetTransactionData = () => {
    setIsResetting(true)

    try {
      // Clear transaction data from localStorage
      localStorage.removeItem('vanity_transactions')
      
      console.log('🔄 RESET: Cleared transaction data from localStorage')
      
      toast({
        title: "Transaction Data Reset",
        description: "Transaction data has been cleared. Refresh the page to load new default transactions.",
      })

      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1500)

    } catch (error) {
      console.error('Error resetting transaction data:', error)
      toast({
        variant: "destructive",
        title: "Reset Failed",
        description: "Error occurred while resetting transaction data",
      })
    }

    setIsResetting(false)
  }

  const clearAllData = () => {
    setIsResetting(true)

    try {
      // Clear all salon data from localStorage
      const keysToRemove = [
        'vanity_transactions',
        'vanity_appointments',
        'vanity_clients',
        'vanity_inventory',
        'vanity_orders'
      ]

      keysToRemove.forEach(key => {
        localStorage.removeItem(key)
        console.log(`🗑️ CLEAR: Removed ${key} from localStorage`)
      })
      
      toast({
        title: "All Data Cleared",
        description: "All salon data has been cleared. Refresh the page to reinitialize.",
      })

      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 1500)

    } catch (error) {
      console.error('Error clearing all data:', error)
      toast({
        variant: "destructive",
        title: "Clear Failed",
        description: "Error occurred while clearing data",
      })
    }

    setIsResetting(false)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reset Transaction Data</h1>
          <p className="text-muted-foreground">
            Reset transaction data to fix date inconsistencies and reload default transactions
          </p>
        </div>
      </div>

      <div className="grid gap-6 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Reset Transactions Only
            </CardTitle>
            <CardDescription>
              Clear only transaction data and reload with current month dates. This will fix the revenue display inconsistency.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                <h4 className="font-semibold text-blue-800">What this will do:</h4>
                <ul className="text-sm text-blue-700 mt-2 space-y-1">
                  <li>• Clear existing transaction data from localStorage</li>
                  <li>• Reload default transactions with current month dates</li>
                  <li>• Fix revenue display inconsistency between test page and dashboard</li>
                  <li>• Preserve all other data (appointments, clients, inventory, etc.)</li>
                </ul>
              </div>
              
              <Button 
                onClick={resetTransactionData} 
                disabled={isResetting}
                className="w-full"
              >
                {isResetting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Resetting...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset Transaction Data
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              Clear All Data
            </CardTitle>
            <CardDescription>
              Clear all salon data and start fresh. Use this for a complete reset.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-red-800">Warning: This action cannot be undone!</h4>
                    <ul className="text-sm text-red-700 mt-2 space-y-1">
                      <li>• Clear all transactions</li>
                      <li>• Clear all appointments</li>
                      <li>• Clear all clients</li>
                      <li>• Clear all inventory</li>
                      <li>• Clear all orders</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={clearAllData} 
                disabled={isResetting}
                variant="destructive"
                className="w-full"
              >
                {isResetting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Clearing...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear All Data
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Current Data Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Transactions:</span>
                <span>{localStorage.getItem('vanity_transactions') ? 'Present' : 'None'}</span>
              </div>
              <div className="flex justify-between">
                <span>Appointments:</span>
                <span>{localStorage.getItem('vanity_appointments') ? 'Present' : 'None'}</span>
              </div>
              <div className="flex justify-between">
                <span>Clients:</span>
                <span>{localStorage.getItem('vanity_clients') ? 'Present' : 'None'}</span>
              </div>
              <div className="flex justify-between">
                <span>Inventory:</span>
                <span>{localStorage.getItem('vanity_inventory') ? 'Present' : 'None'}</span>
              </div>
              <div className="flex justify-between">
                <span>Orders:</span>
                <span>{localStorage.getItem('vanity_orders') ? 'Present' : 'None'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
