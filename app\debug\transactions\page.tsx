"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useTransactions } from "@/lib/transaction-provider"
import { TransactionType, TransactionSource, TransactionStatus, PaymentMethod } from "@/lib/transaction-types"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { CheckCircle, ShoppingCart, Calendar, CreditCard, RefreshCw, Trash2, Database, RotateCcw, Clock } from "lucide-react"
import { initializeAllData, resetAndInitializeData, clearAllData, getDataStatistics } from "@/lib/data-initialization"
import { ServiceStorage } from "@/lib/service-storage"

export default function TransactionDebugPage() {
  const { transactions, addTransaction, getTransactionsBySource, filterTransactions } = useTransactions()
  const { toast } = useToast()
  const [isTestRunning, setIsTestRunning] = useState(false)

  // Test client portal transaction (matching real checkout process)
  const testClientPortalTransaction = () => {
    setIsTestRunning(true)

    const transaction = {
      date: new Date(),
      clientId: "debug-client-123",
      clientName: "Debug Test Client",
      type: TransactionType.PRODUCT_SALE,
      category: "Online Product Sale",
      description: "Client Portal Order - 1 item",
      amount: 99.99,
      paymentMethod: PaymentMethod.CREDIT_CARD,
      status: TransactionStatus.COMPLETED,
      location: "online", // Use "online" location like real client portal
      source: TransactionSource.CLIENT_PORTAL,
      reference: {
        type: "client_portal_order",
        id: `debug-order-${Date.now()}`
      },
      metadata: {
        orderData: {
          id: `debug-order-${Date.now()}`,
          clientId: "debug-client-123",
          items: [
            {
              product: {
                id: "debug-product-1",
                name: "Debug Test Product",
                price: 99.99
              },
              quantity: 1
            }
          ],
          subtotal: 99.99,
          total: 99.99,
          paymentMethod: "card",
          shippingAddress: {
            street: "123 Debug Street",
            city: "Test City",
            state: "TC",
            zipCode: "12345"
          },
          status: "completed",
          createdAt: new Date().toISOString()
        },
        itemCount: 1,
        appliedPromo: null,
        isOnlineTransaction: true // Flag to identify online transactions
      }
    }

    console.log('🛒 DEBUG: Creating client portal transaction (matching real checkout):', transaction)
    const result = addTransaction(transaction)
    console.log('✅ DEBUG: Transaction creation result:', result)

    toast({
      title: "Client Portal Transaction Created",
      description: `Transaction ${result.id} created with location "online"`,
    })

    setIsTestRunning(false)
  }

  // Test filtering specifically for client portal transactions
  const testClientPortalFiltering = () => {
    console.log('🔍 DEBUG: Testing client portal transaction filtering...')

    // Get all transactions
    const allTx = transactions
    console.log('Total transactions:', allTx.length)

    // Filter by CLIENT_PORTAL source
    const clientPortalTx = allTx.filter(tx => tx.source === TransactionSource.CLIENT_PORTAL)
    console.log('CLIENT_PORTAL transactions:', clientPortalTx.length)

    // Test location filtering
    const onlineLocationTx = allTx.filter(tx => tx.location === 'online')
    console.log('Transactions with location "online":', onlineLocationTx.length)

    const testFilters = [
      { name: 'All', filter: {} },
      { name: 'Location: all', filter: { location: 'all' } },
      { name: 'Location: loc1', filter: { location: 'loc1' } },
      { name: 'Location: online', filter: { location: 'online' } },
      { name: 'Source: CLIENT_PORTAL', filter: { source: TransactionSource.CLIENT_PORTAL } }
    ]

    testFilters.forEach(test => {
      const filtered = filterTransactions(test.filter)
      const clientPortalInFiltered = filtered.filter(tx => tx.source === TransactionSource.CLIENT_PORTAL).length
      console.log(`🔍 ${test.name}: ${filtered.length} total, ${clientPortalInFiltered} CLIENT_PORTAL`)
    })

    toast({
      title: "Filter Test Complete",
      description: "Check console for detailed filtering results",
    })
  }

  // Test appointment transaction
  const testAppointmentTransaction = () => {
    setIsTestRunning(true)

    const transaction = {
      date: new Date(),
      clientId: "debug-client-456",
      clientName: "Debug Appointment Client",
      staffId: "1",
      staffName: "Emma Johnson",
      type: TransactionType.SERVICE_SALE,
      category: "Appointment Service",
      description: "DEBUG: Completed appointment - Haircut & Style",
      amount: 75.00,
      paymentMethod: PaymentMethod.CASH,
      status: TransactionStatus.COMPLETED,
      location: "loc1",
      source: TransactionSource.CALENDAR,
      reference: {
        type: "appointment",
        id: `debug-appointment-${Date.now()}`
      },
      items: [
        {
          id: "service-1",
          name: "Haircut & Style",
          quantity: 1,
          unitPrice: 75.00,
          totalPrice: 75.00,
          category: "Service"
        }
      ],
      metadata: {
        appointmentId: `debug-appointment-${Date.now()}`,
        bookingReference: `VH-${Date.now().toString().slice(-6)}`,
        appointmentDate: new Date().toISOString(),
        duration: 60,
        completedAt: new Date().toISOString(),
        debugTest: true
      }
    }

    console.log('Creating debug appointment transaction:', transaction)
    const result = addTransaction(transaction)
    console.log('Transaction creation result:', result)

    toast({
      title: "Debug Transaction Created",
      description: `Appointment transaction ${result.id} has been recorded.`,
    })

    setIsTestRunning(false)
  }

  // Comprehensive appointment system test
  const testAppointmentSystem = () => {
    setIsTestRunning(true)

    try {
      console.log('=== COMPREHENSIVE APPOINTMENT SYSTEM TEST ===')

      // 1. Check current data state
      const storedAppointments = localStorage.getItem('vanity_appointments')
      const storedTransactions = localStorage.getItem('vanity_transactions')

      console.log('Current Data State:')
      console.log('- Appointments in localStorage:', storedAppointments ? JSON.parse(storedAppointments).length : 0)
      console.log('- Transactions in localStorage:', storedTransactions ? JSON.parse(storedTransactions).length : 0)
      console.log('- Transactions in provider:', transactions.length)
      console.log('- Calendar transactions:', transactions.filter(tx => tx.source === TransactionSource.CALENDAR).length)

      // 2. Check completed appointments
      if (storedAppointments) {
        const appointments = JSON.parse(storedAppointments)
        const completedAppointments = appointments.filter((apt: any) => apt.status === 'completed')

        console.log('Appointment Analysis:')
        console.log('- Total appointments:', appointments.length)
        console.log('- Completed appointments:', completedAppointments.length)

        completedAppointments.forEach((apt: any, index: number) => {
          const existingTransaction = transactions.find(tx =>
            tx.reference?.type === 'appointment' && tx.reference?.id === apt.id
          )

          console.log(`  ${index + 1}. ${apt.clientName} - ${apt.service}:`)
          console.log(`     - Price: ${apt.price}`)
          console.log(`     - Has Transaction: ${!!existingTransaction}`)
          console.log(`     - Transaction ID: ${existingTransaction?.id || 'None'}`)
        })
      }

      // 3. Test transaction visibility across interfaces
      console.log('Transaction Visibility Test:')
      console.log('- Accounting page should show:', transactions.length, 'transactions')
      console.log('- Dashboard analytics should include calendar transactions')
      console.log('- Reports should include service revenue from appointments')

      toast({
        title: "Appointment System Test Complete",
        description: "Check console for detailed analysis results",
      })

    } catch (error) {
      console.error('Error during appointment system test:', error)
      toast({
        variant: "destructive",
        title: "Test Failed",
        description: "Error occurred during system test",
      })
    }

    setIsTestRunning(false)
  }

  // Test pending revenue calculation
  const testPendingRevenue = () => {
    setIsTestRunning(true)

    try {
      console.log('=== PENDING REVENUE TEST ===')

      // Get current appointments
      const storedAppointments = localStorage.getItem('vanity_appointments')
      const appointments = storedAppointments ? JSON.parse(storedAppointments) : []

      console.log('Current appointments:', appointments.length)

      // Create test appointments with different statuses
      const testAppointments = [
        {
          id: `pending-test-${Date.now()}-1`,
          clientId: "test-client-1",
          clientName: "Test Client 1",
          staffId: "1",
          staffName: "Emma Johnson",
          service: "Haircut & Style",
          serviceId: "1",
          date: new Date().toISOString(),
          duration: 60,
          status: "confirmed", // Should contribute to pending revenue
          location: "loc1",
          price: 75,
          bookingReference: `TEST-${Date.now()}-1`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          additionalServices: [
            { name: "Hair Wash", price: 15 }
          ],
          products: [
            { name: "Hair Product", price: 25 }
          ]
        },
        {
          id: `pending-test-${Date.now()}-2`,
          clientId: "test-client-2",
          clientName: "Test Client 2",
          staffId: "2",
          staffName: "Michael Chen",
          service: "Color Treatment",
          serviceId: "2",
          date: new Date().toISOString(),
          duration: 120,
          status: "arrived", // Should contribute to pending revenue
          location: "loc1",
          price: 120,
          bookingReference: `TEST-${Date.now()}-2`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: `pending-test-${Date.now()}-3`,
          clientId: "test-client-3",
          clientName: "Test Client 3",
          staffId: "1",
          staffName: "Emma Johnson",
          service: "Manicure",
          serviceId: "3",
          date: new Date().toISOString(),
          duration: 45,
          status: "completed", // Should NOT contribute to pending revenue
          location: "loc1",
          price: 50,
          bookingReference: `TEST-${Date.now()}-3`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      // Add test appointments to existing appointments
      const updatedAppointments = [...appointments, ...testAppointments]
      localStorage.setItem('vanity_appointments', JSON.stringify(updatedAppointments))

      console.log('Added test appointments:', testAppointments.length)
      console.log('Expected pending revenue:')
      console.log('- Appointment 1 (confirmed): 75 + 15 + 25 = 115')
      console.log('- Appointment 2 (arrived): 120')
      console.log('- Appointment 3 (completed): 0 (should not count)')
      console.log('- Total expected: 235')

      toast({
        title: "Pending Revenue Test Created",
        description: "Added test appointments. Check dashboard for pending revenue calculation.",
      })

    } catch (error) {
      console.error('Error creating pending revenue test:', error)
      toast({
        variant: "destructive",
        title: "Test Failed",
        description: "Error occurred during pending revenue test",
      })
    }

    setIsTestRunning(false)
  }

  // Debug pending revenue calculation
  const debugPendingRevenue = () => {
    setIsTestRunning(true)

    try {
      console.log('=== PENDING REVENUE DEBUG ===')

      // Get current appointments
      const storedAppointments = localStorage.getItem('vanity_appointments')
      const appointments = storedAppointments ? JSON.parse(storedAppointments) : []

      console.log('All appointments:', appointments.length)

      // Filter for pending statuses
      const pendingStatuses = ['confirmed', 'arrived', 'service-started']
      const pendingAppointments = appointments.filter((apt: any) =>
        pendingStatuses.includes(apt.status)
      )

      console.log('Pending appointments:', pendingAppointments.length)

      pendingAppointments.forEach((apt: any, index: number) => {
        console.log(`\n--- APPOINTMENT ${index + 1} ---`)
        console.log('ID:', apt.id)
        console.log('Client:', apt.clientName)
        console.log('Service:', apt.service)
        console.log('Status:', apt.status)
        console.log('Location:', apt.location)
        console.log('Price:', apt.price, '(type:', typeof apt.price, ')')
        console.log('Additional Services:', apt.additionalServices)
        console.log('Products:', apt.products)

        // Calculate total for this appointment
        let total = 0
        if (typeof apt.price === 'number') {
          total += apt.price
        }
        if (apt.additionalServices) {
          apt.additionalServices.forEach((service: any) => {
            if (typeof service.price === 'number') {
              total += service.price
            }
          })
        }
        if (apt.products) {
          apt.products.forEach((product: any) => {
            if (typeof product.price === 'number') {
              total += product.price
            }
          })
        }
        console.log('Calculated Total:', total)

        // Check for existing transactions
        const existingTransaction = transactions.find(tx =>
          tx.reference?.type === 'appointment' &&
          tx.reference?.id === apt.id &&
          tx.status === TransactionStatus.COMPLETED
        )
        console.log('Has completed transaction:', !!existingTransaction)
      })

      toast({
        title: "Pending Revenue Debug Complete",
        description: "Check console for detailed appointment analysis",
      })

    } catch (error) {
      console.error('Error during pending revenue debug:', error)
      toast({
        variant: "destructive",
        title: "Debug Failed",
        description: "Error occurred during debug",
      })
    }

    setIsTestRunning(false)
  }

  // Debug pending revenue calculation exactly like stats card
  const debugPendingRevenueCalculation = () => {
    setIsTestRunning(true)

    try {
      console.log('=== PENDING REVENUE CALCULATION DEBUG ===')

      // Get current appointments (same as stats card)
      const allAppointments = localStorage.getItem('vanity_appointments')
      const appointments = allAppointments ? JSON.parse(allAppointments) : []

      console.log('📊 All appointments:', appointments.length)

      // Filter appointments that contribute to pending revenue (same logic as stats card)
      const pendingStatuses = ['confirmed', 'arrived', 'service-started']
      const pendingAppointments = appointments.filter((appointment: any) => {
        // Only include appointments with pending statuses
        if (!pendingStatuses.includes(appointment.status)) {
          return false
        }

        // Note: Not filtering by location here to see all pending appointments
        return true
      })

      console.log('📊 Pending appointments found:', pendingAppointments.length)

      let totalPendingRevenue = 0
      let serviceRevenue = 0
      let productRevenue = 0

      pendingAppointments.forEach((appointment: any, index: number) => {
        console.log(`\n--- PROCESSING APPOINTMENT ${index + 1} ---`)
        console.log('ID:', appointment.id)
        console.log('Client:', appointment.clientName)
        console.log('Service:', appointment.service)
        console.log('Status:', appointment.status)
        console.log('Location:', appointment.location)
        console.log('Raw Price:', appointment.price, '(type:', typeof appointment.price, ')')

        // Check if this appointment already has a completed transaction
        const existingTransaction = transactions.find(tx =>
          tx.reference?.type === 'appointment' &&
          tx.reference?.id === appointment.id &&
          tx.status === TransactionStatus.COMPLETED
        )

        console.log('Has completed transaction:', !!existingTransaction)

        if (!existingTransaction) {
          // Calculate appointment revenue step by step
          let appointmentTotal = 0

          // Main service price
          let mainServicePrice = 0
          if (typeof appointment.price === 'number') {
            mainServicePrice = appointment.price
            console.log('✅ Using appointment price (number):', mainServicePrice)
          } else if (typeof appointment.price === 'string' && !isNaN(parseFloat(appointment.price))) {
            mainServicePrice = parseFloat(appointment.price)
            console.log('✅ Using appointment price (converted from string):', mainServicePrice)
          } else {
            console.log('❌ Invalid appointment price, trying service lookup...')
            console.log('Service name:', appointment.service)
            console.log('Service ID:', appointment.serviceId)

            // Try actual service lookup
            try {
              const services = ServiceStorage.getServices()
              console.log('Available services:', services.length)
              console.log('Service names:', services.map(s => s.name))

              const service = services.find(s => s.name === appointment.service || s.id === appointment.serviceId)
              if (service) {
                mainServicePrice = service.price
                console.log('✅ Found service price via lookup:', mainServicePrice, 'for service:', service.name)
              } else {
                console.log('❌ Service not found in catalog')
                console.log('Looking for service name:', appointment.service)
                console.log('Looking for service ID:', appointment.serviceId)
              }
            } catch (error) {
              console.log('❌ Service lookup error:', error)
            }
          }

          if (mainServicePrice > 0) {
            appointmentTotal += mainServicePrice
            serviceRevenue += mainServicePrice
            console.log('✅ Added main service revenue:', mainServicePrice)
          } else {
            console.log('❌ No main service revenue added')
          }

          // Additional services
          if (appointment.additionalServices && appointment.additionalServices.length > 0) {
            console.log('📋 Processing additional services:', appointment.additionalServices.length)
            appointment.additionalServices.forEach((service: any, idx: number) => {
              console.log(`  Service ${idx + 1}:`, service.name, 'Price:', service.price, 'Type:', typeof service.price)
              if (typeof service.price === 'number' && service.price > 0) {
                appointmentTotal += service.price
                serviceRevenue += service.price
                console.log(`  ✅ Added additional service revenue:`, service.price)
              }
            })
          }

          // Products
          if (appointment.products && appointment.products.length > 0) {
            console.log('📦 Processing products:', appointment.products.length)
            appointment.products.forEach((product: any, idx: number) => {
              console.log(`  Product ${idx + 1}:`, product.name, 'Price:', product.price, 'Type:', typeof product.price)
              if (typeof product.price === 'number' && product.price > 0) {
                const quantity = product.quantity || 1
                const productTotal = product.price * quantity
                appointmentTotal += productTotal
                productRevenue += productTotal
                console.log(`  ✅ Added product revenue:`, productTotal, `(${product.price} x ${quantity})`)
              }
            })
          }

          totalPendingRevenue += appointmentTotal

          console.log('💰 APPOINTMENT TOTAL:', appointmentTotal)
          console.log('💰 RUNNING TOTAL:', totalPendingRevenue)
        } else {
          console.log('⏭️ SKIPPED - has completed transaction')
        }
      })

      console.log('\n=== FINAL RESULTS ===')
      console.log('Total Pending Revenue:', totalPendingRevenue)
      console.log('Service Revenue:', serviceRevenue)
      console.log('Product Revenue:', productRevenue)
      console.log('Appointment Count:', pendingAppointments.filter(apt =>
        !transactions.find(tx =>
          tx.reference?.type === 'appointment' &&
          tx.reference?.id === apt.id &&
          tx.status === TransactionStatus.COMPLETED
        )
      ).length)

      toast({
        title: "Pending Revenue Calculation Debug Complete",
        description: `Found ${pendingAppointments.length} pending appointments with total revenue: ${totalPendingRevenue}`,
      })

    } catch (error) {
      console.error('Error during pending revenue calculation debug:', error)
      toast({
        variant: "destructive",
        title: "Debug Failed",
        description: "Error occurred during calculation debug",
      })
    }

    setIsTestRunning(false)
  }

  // Create test appointments with proper prices for pending revenue testing
  const createTestAppointmentsWithPrices = () => {
    setIsTestRunning(true)

    try {
      console.log('=== CREATING TEST APPOINTMENTS WITH PRICES ===')

      // Get current appointments
      const storedAppointments = localStorage.getItem('vanity_appointments')
      const appointments = storedAppointments ? JSON.parse(storedAppointments) : []

      // Clear existing test appointments
      const filteredAppointments = appointments.filter((apt: any) =>
        !apt.id.includes('price-test')
      )

      // Create test appointments with explicit prices
      const testAppointments = [
        {
          id: `price-test-${Date.now()}-1`,
          clientId: "test-client-1",
          clientName: "Test Client 1",
          staffId: "1",
          staffName: "Emma Johnson",
          service: "Haircut & Style",
          serviceId: "1",
          date: new Date().toISOString(),
          duration: 60,
          status: "confirmed", // Should contribute to pending revenue
          location: "loc1",
          price: 75, // Explicit number price
          bookingReference: `PRICE-TEST-${Date.now()}-1`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: `price-test-${Date.now()}-2`,
          clientId: "test-client-2",
          clientName: "Test Client 2",
          staffId: "2",
          staffName: "Michael Chen",
          service: "Color Treatment",
          serviceId: "2",
          date: new Date().toISOString(),
          duration: 120,
          status: "arrived", // Should contribute to pending revenue
          location: "loc2",
          price: 120, // Explicit number price
          bookingReference: `PRICE-TEST-${Date.now()}-2`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      // Add test appointments to existing appointments
      const updatedAppointments = [...filteredAppointments, ...testAppointments]
      localStorage.setItem('vanity_appointments', JSON.stringify(updatedAppointments))

      console.log('Created test appointments with explicit prices:')
      testAppointments.forEach((apt, index) => {
        console.log(`${index + 1}. ${apt.clientName} - ${apt.service}: $${apt.price} (${apt.status})`)
      })
      console.log('Expected pending revenue: $195 (75 + 120)')

      toast({
        title: "Test Appointments Created",
        description: "Created 2 test appointments with explicit prices. Check dashboard pending revenue.",
      })

    } catch (error) {
      console.error('Error creating test appointments:', error)
      toast({
        variant: "destructive",
        title: "Test Failed",
        description: "Error occurred during test appointment creation",
      })
    }

    setIsTestRunning(false)
  }

  // Create transactions for existing completed appointments
  const createMissingAppointmentTransactions = () => {
    setIsTestRunning(true)

    try {
      // Get appointments from localStorage
      const storedAppointments = localStorage.getItem('vanity_appointments')
      if (!storedAppointments) {
        toast({
          variant: "destructive",
          title: "No Appointments Found",
          description: "No appointments found in localStorage.",
        })
        setIsTestRunning(false)
        return
      }

      const appointments = JSON.parse(storedAppointments)
      const completedAppointments = appointments.filter((apt: any) => apt.status === 'completed')

      console.log('=== APPOINTMENT TRANSACTION TEST ===')
      console.log('Total appointments:', appointments.length)
      console.log('Completed appointments:', completedAppointments.length)
      console.log('Current transactions:', transactions.length)
      console.log('Calendar transactions:', transactions.filter(tx => tx.source === TransactionSource.CALENDAR).length)

      let createdCount = 0

      completedAppointments.forEach((appointment: any) => {
        // Check if transaction already exists
        const existingTransaction = transactions.find(tx =>
          tx.reference?.type === 'appointment' && tx.reference?.id === appointment.id
        )

        console.log(`Appointment ${appointment.id} (${appointment.clientName}):`, {
          service: appointment.service,
          price: appointment.price,
          hasExistingTransaction: !!existingTransaction,
          existingTransactionId: existingTransaction?.id
        })

        if (!existingTransaction && appointment.price > 0) {
          const transaction = {
            date: new Date(appointment.date),
            clientId: appointment.clientId,
            clientName: appointment.clientName,
            staffId: appointment.staffId,
            staffName: appointment.staffName,
            type: TransactionType.SERVICE_SALE,
            category: "Appointment Service",
            description: `Completed appointment - ${appointment.service}`,
            amount: appointment.price,
            paymentMethod: PaymentMethod.CASH,
            status: TransactionStatus.COMPLETED,
            location: appointment.location,
            source: TransactionSource.CALENDAR,
            reference: {
              type: "appointment",
              id: appointment.id
            },
            items: [
              {
                id: `service-${appointment.service}`,
                name: appointment.service,
                quantity: 1,
                unitPrice: appointment.price,
                totalPrice: appointment.price,
                category: "Service"
              }
            ],
            metadata: {
              appointmentId: appointment.id,
              bookingReference: `VH-${appointment.id}`,
              appointmentDate: appointment.date,
              duration: appointment.duration,
              completedAt: new Date().toISOString()
            }
          }

          console.log('Creating transaction for appointment:', appointment.id)
          addTransaction(transaction)
          createdCount++
        }
      })

      toast({
        title: "Missing Transactions Created",
        description: `Created ${createdCount} transactions for completed appointments.`,
      })

    } catch (error) {
      console.error('Error creating missing transactions:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create missing transactions.",
      })
    }

    setIsTestRunning(false)
  }

  // Clear all transactions
  const clearAllTransactions = () => {
    localStorage.removeItem('vanity_transactions')
    window.location.reload()
  }

  // Initialize all data from mock
  const handleInitializeAllData = () => {
    setIsTestRunning(true)
    try {
      initializeAllData()
      toast({
        title: "Data Initialized",
        description: "All mock data has been converted to real data in localStorage.",
      })
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('Error initializing data:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to initialize data.",
      })
    }
    setIsTestRunning(false)
  }

  // Reset and reinitialize all data
  const handleResetAndInitialize = () => {
    setIsTestRunning(true)
    try {
      resetAndInitializeData()
      toast({
        title: "Data Reset and Initialized",
        description: "All data has been cleared and reinitialized from mock data.",
      })
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('Error resetting data:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to reset and initialize data.",
      })
    }
    setIsTestRunning(false)
  }

  // Clear all data
  const handleClearAllData = () => {
    setIsTestRunning(true)
    try {
      clearAllData()
      toast({
        title: "All Data Cleared",
        description: "All data has been removed from localStorage.",
      })
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('Error clearing data:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to clear data.",
      })
    }
    setIsTestRunning(false)
  }

  // Get transaction counts by source
  const clientPortalTransactions = getTransactionsBySource(TransactionSource.CLIENT_PORTAL)
  const calendarTransactions = getTransactionsBySource(TransactionSource.CALENDAR)
  const allTransactions = transactions

  // Check localStorage directly
  const [localStorageData, setLocalStorageData] = useState<any>(null)
  const [dataStats, setDataStats] = useState<any>(null)

  useEffect(() => {
    const checkLocalStorage = () => {
      const stored = localStorage.getItem('vanity_transactions')
      if (stored) {
        try {
          const parsed = JSON.parse(stored)
          setLocalStorageData(parsed)
        } catch (error) {
          setLocalStorageData({ error: 'Failed to parse' })
        }
      } else {
        setLocalStorageData(null)
      }

      // Get data statistics
      try {
        const stats = getDataStatistics()
        setDataStats(stats)
      } catch (error) {
        setDataStats({ error: 'Failed to get stats' })
      }
    }

    checkLocalStorage()
    // Check every 2 seconds
    const interval = setInterval(checkLocalStorage, 2000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Transaction Debug Page</h1>
        <p className="text-gray-600 mt-2">
          Debug and test transaction recording functionality.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Transaction Counts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-blue-500" />
              Transaction Counts
            </CardTitle>
            <CardDescription>
              Current transaction counts in memory
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total Transactions:</span>
                <Badge variant="outline">{allTransactions.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Client Portal:</span>
                <Badge variant="outline">{clientPortalTransactions.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Calendar/Appointments:</span>
                <Badge variant="outline">{calendarTransactions.length}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* LocalStorage Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 text-green-500" />
              LocalStorage Status
            </CardTitle>
            <CardDescription>
              Current localStorage data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Stored Count:</span>
                <Badge variant="outline">
                  {localStorageData ? (Array.isArray(localStorageData) ? localStorageData.length : 'Error') : '0'}
                </Badge>
              </div>
              <div className="text-xs text-gray-500">
                {localStorageData ? 'Data found' : 'No data'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-orange-500" />
              Data Statistics
            </CardTitle>
            <CardDescription>
              Current data counts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {dataStats ? (
                <>
                  <div className="flex justify-between">
                    <span>Appointments:</span>
                    <Badge variant="outline">{dataStats.appointments}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Completed:</span>
                    <Badge variant="outline">{dataStats.completedAppointments}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Calendar Transactions:</span>
                    <Badge variant="outline">{dataStats.calendarTransactions}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Transactions:</span>
                    <Badge variant="outline">{dataStats.transactions}</Badge>
                  </div>
                </>
              ) : (
                <div className="text-xs text-gray-500">Loading stats...</div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-purple-500" />
              Debug Actions
            </CardTitle>
            <CardDescription>
              Test and debug functions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              onClick={testClientPortalTransaction}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Test Client Portal
            </Button>
            <Button
              onClick={testClientPortalFiltering}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="secondary"
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              Test Filtering
            </Button>
            <Button
              onClick={testAppointmentTransaction}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="outline"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Test Appointment
            </Button>
            <Button
              onClick={createMissingAppointmentTransactions}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="secondary"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Create Missing Transactions
            </Button>
            <Button
              onClick={testAppointmentSystem}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="outline"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Test System Integration
            </Button>
            <Button
              onClick={testPendingRevenue}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="outline"
            >
              <Clock className="mr-2 h-4 w-4" />
              Test Pending Revenue
            </Button>
            <Button
              onClick={debugPendingRevenue}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="secondary"
            >
              <Database className="mr-2 h-4 w-4" />
              Debug Pending Revenue
            </Button>
            <Button
              onClick={debugPendingRevenueCalculation}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="secondary"
            >
              <Database className="mr-2 h-4 w-4" />
              Debug Revenue Calculation
            </Button>
            <Button
              onClick={createTestAppointmentsWithPrices}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="outline"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Create Test Appointments
            </Button>
            <Button
              onClick={clearAllTransactions}
              variant="destructive"
              className="w-full"
              size="sm"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Clear Transactions
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Data Management Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-500" />
              Data Management
            </CardTitle>
            <CardDescription>
              Initialize and manage data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              onClick={handleInitializeAllData}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="default"
            >
              <Database className="mr-2 h-4 w-4" />
              Initialize All Data
            </Button>
            <Button
              onClick={handleResetAndInitialize}
              disabled={isTestRunning}
              className="w-full"
              size="sm"
              variant="outline"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset & Initialize
            </Button>
            <Button
              onClick={handleClearAllData}
              disabled={isTestRunning}
              variant="destructive"
              className="w-full"
              size="sm"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Clear All Data
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>All Transactions (Debug View)</CardTitle>
          <CardDescription>
            Raw transaction data from the provider
          </CardDescription>
        </CardHeader>
        <CardContent>
          {allTransactions.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              No transactions found in memory.
            </p>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {allTransactions.slice(-10).reverse().map((transaction) => (
                <div key={transaction.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold text-sm">{transaction.id}</h3>
                      <p className="text-xs text-gray-600">{transaction.description}</p>
                      <p className="text-xs text-gray-500">
                        {transaction.clientName} • {new Date(transaction.date).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-sm">
                        <CurrencyDisplay amount={transaction.amount} />
                      </div>
                      <Badge variant="outline" className="mt-1 text-xs">
                        {transaction.source}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>{transaction.paymentMethod}</span>
                    <span>{transaction.status}</span>
                  </div>
                  {transaction.metadata && (
                    <div className="mt-2 text-xs text-gray-400">
                      <details>
                        <summary className="cursor-pointer">Metadata</summary>
                        <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                          {JSON.stringify(transaction.metadata, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* LocalStorage Raw Data */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>LocalStorage Raw Data</CardTitle>
          <CardDescription>
            Raw data stored in localStorage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-x-auto max-h-64">
            {localStorageData ? JSON.stringify(localStorageData, null, 2) : 'No data in localStorage'}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
