import type React from "react"
import { Inter } from "next/font/google"
import dynamic from "next/dynamic"
import "./globals.css"

// Import essential providers first
const ThemeProvider = dynamic(() => import("@/components/theme-provider").then(mod => mod.ThemeProvider))
const ClientToaster = dynamic(() => import("@/components/client-toaster").then(mod => mod.ClientToaster))
const LocationProvider = dynamic(() => import("@/lib/location-provider").then(mod => mod.LocationProvider))
const AuthProvider = dynamic(() => import("@/lib/auth-provider").then(mod => mod.AuthProvider))
const ServiceProvider = dynamic(() => import("@/lib/service-provider").then(mod => mod.ServiceProvider))
const ClientProvider = dynamic(() => import("@/lib/client-provider").then(mod => mod.ClientProvider))
const CurrencyProvider = dynamic(() => import("@/lib/currency-provider").then(mod => mod.CurrencyProvider))
const StaffProvider = dynamic(() => import("@/lib/staff-provider").then(mod => mod.StaffProvider))
const UnifiedStaffProvider = dynamic(() => import("@/lib/unified-staff-provider").then(mod => mod.UnifiedStaffProvider))
const ScheduleProvider = dynamic(() => import("@/lib/schedule-provider").then(mod => mod.ScheduleProvider))
const ProductProvider = dynamic(() => import("@/lib/product-provider").then(mod => mod.ProductProvider))
const TransactionProvider = dynamic(() => import("@/lib/transaction-provider").then(mod => mod.TransactionProvider))
const OrderProvider = dynamic(() => import("@/lib/order-provider").then(mod => mod.OrderProvider))
const ClientCurrencyWrapper = dynamic(() => import("@/components/client-currency-wrapper").then(mod => mod.ClientCurrencyWrapper))
const MobileProvider = dynamic(() => import("@/hooks/use-mobile").then(mod => mod.MobileProvider))
const DataInitializer = dynamic(() => import("@/components/data-initializer").then(mod => mod.DataInitializer))
const LocationMigrationRunner = dynamic(() => import("@/components/migration/location-migration-runner").then(mod => mod.LocationMigrationRunner))

const RealTimeNotifications = dynamic(() => import("@/components/real-time/real-time-notifications").then(mod => mod.RealTimeNotifications))

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Vanity | Modern Salon Management",
  description: "Multi-location salon management application for beauty professionals",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <MobileProvider>
            <LocationProvider>
              <ServiceProvider>
                <AuthProvider>
                  <StaffProvider>
                    <UnifiedStaffProvider>
                      <ClientProvider>
                      <CurrencyProvider>
                        <ProductProvider>
                          <TransactionProvider>
                            <OrderProvider>
                              <ScheduleProvider>
                                <ClientCurrencyWrapper>
                                  <LocationMigrationRunner />
                                  <DataInitializer />
                                  {children}
                                </ClientCurrencyWrapper>
                              </ScheduleProvider>
                            </OrderProvider>
                          </TransactionProvider>
                        </ProductProvider>
                      </CurrencyProvider>
                      </ClientProvider>
                    </UnifiedStaffProvider>
                  </StaffProvider>
                </AuthProvider>
              </ServiceProvider>
            </LocationProvider>
          </MobileProvider>
          <ClientToaster />
          <RealTimeNotifications />
        </ThemeProvider>
      </body>
    </html>
  )
}

