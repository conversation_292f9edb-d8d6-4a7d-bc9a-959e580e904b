import { PrismaClient, UserRole, StaffStatus, AppointmentStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { realServiceData } from './real-services-data'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create admin user (or get existing one)
  const adminPassword = await bcrypt.hash('admin123', 10)
  let admin = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!admin) {
    admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: adminPassword,
        role: UserRole.ADMIN,
      },
    })
    console.log('✅ Created admin user')
  } else {
    console.log('ℹ️ Admin user already exists')
  }

  // Create locations
  const mainLocation = await prisma.location.create({
    data: {
      name: 'Vanity Hub - Main Branch',
      address: '123 Beauty Street',
      city: 'Doha',
      state: 'Doha',
      zipCode: '12345',
      country: 'Qatar',
      phone: '+974 1234 5678',
      email: '<EMAIL>',
    },
  })

  const branchLocation = await prisma.location.create({
    data: {
      name: 'Vanity Hub - West Bay',
      address: '456 Glamour Avenue',
      city: 'Doha',
      state: 'Doha',
      zipCode: '12346',
      country: 'Qatar',
      phone: '+974 1234 5679',
      email: '<EMAIL>',
    },
  })

  // Clear existing services first
  console.log('🧹 Clearing existing services...')
  await prisma.appointmentService.deleteMany()
  await prisma.locationService.deleteMany()
  await prisma.staffService.deleteMany()
  await prisma.service.deleteMany()

  console.log(`🌱 Creating ${realServiceData.length} real salon services...`)
  const services = []

  for (const serviceData of realServiceData) {
    const service = await prisma.service.create({
      data: {
        name: serviceData.name,
        description: `Professional ${serviceData.name.toLowerCase()} service`,
        duration: serviceData.duration,
        price: serviceData.price,
        category: serviceData.category,
      },
    })
    services.push(service)
  }

  // Associate all services with all locations
  console.log('🔗 Associating services with locations...')
  for (const service of services) {
    // Associate with main location
    await prisma.locationService.create({
      data: {
        locationId: mainLocation.id,
        serviceId: service.id,
        price: service.price, // Use same price for all locations
      },
    })

    // Associate with branch location
    await prisma.locationService.create({
      data: {
        locationId: branchLocation.id,
        serviceId: service.id,
        price: service.price, // Use same price for all locations
      },
    })
  }

  // Create staff members
  const staff1Password = await bcrypt.hash('staff123', 10)
  const staff1User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: staff1Password,
      role: UserRole.STAFF,
    },
  })

  const staff1 = await prisma.staffMember.create({
    data: {
      userId: staff1User.id,
      name: 'Sarah Johnson',
      phone: '+974 1234 5680',
      color: '#FF6B6B',
      homeService: false,
      status: StaffStatus.ACTIVE,
    },
  })

  const staff2Password = await bcrypt.hash('staff123', 10)
  const staff2User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: staff2Password,
      role: UserRole.STAFF,
    },
  })

  const staff2 = await prisma.staffMember.create({
    data: {
      userId: staff2User.id,
      name: 'Maria Garcia',
      phone: '+974 1234 5681',
      color: '#4ECDC4',
      homeService: true,
      status: StaffStatus.ACTIVE,
    },
  })

  // Create client users
  const client1Password = await bcrypt.hash('client123', 10)
  const client1User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: client1Password,
      role: UserRole.CLIENT,
    },
  })

  const client1 = await prisma.client.create({
    data: {
      userId: client1User.id,
      name: 'Emma Wilson',
      phone: '+974 1234 5682',
      preferences: 'Prefers morning appointments',
    },
  })

  const client2Password = await bcrypt.hash('client123', 10)
  const client2User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: client2Password,
      role: UserRole.CLIENT,
    },
  })

  const client2 = await prisma.client.create({
    data: {
      userId: client2User.id,
      name: 'Fatima Al-Rashid',
      phone: '+974 1234 5683',
      preferences: 'Allergic to certain hair products',
    },
  })

  // Link staff to locations
  await Promise.all([
    prisma.staffLocation.create({
      data: {
        staffId: staff1.id,
        locationId: mainLocation.id,
      },
    }),
    prisma.staffLocation.create({
      data: {
        staffId: staff2.id,
        locationId: mainLocation.id,
      },
    }),
    prisma.staffLocation.create({
      data: {
        staffId: staff2.id,
        locationId: branchLocation.id,
      },
    }),
  ])

  // Services are already linked to locations above, so skip this duplicate step

  // Link staff to services
  await Promise.all([
    // Sarah - Hair specialist
    prisma.staffService.create({
      data: {
        staffId: staff1.id,
        serviceId: services[0].id, // Haircut
      },
    }),
    prisma.staffService.create({
      data: {
        staffId: staff1.id,
        serviceId: services[1].id, // Hair Coloring
      },
    }),
    // Maria - Multi-service
    prisma.staffService.create({
      data: {
        staffId: staff2.id,
        serviceId: services[2].id, // Manicure
      },
    }),
    prisma.staffService.create({
      data: {
        staffId: staff2.id,
        serviceId: services[3].id, // Pedicure
      },
    }),
    prisma.staffService.create({
      data: {
        staffId: staff2.id,
        serviceId: services[4].id, // Facial
      },
    }),
  ])

  // Create sample appointments
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(10, 0, 0, 0)

  const appointment1 = await prisma.appointment.create({
    data: {
      bookingReference: 'VH-001',
      clientId: client1User.id,
      staffId: staff1.id,
      locationId: mainLocation.id,
      date: tomorrow,
      duration: 60,
      totalPrice: 150,
      status: AppointmentStatus.CONFIRMED,
      notes: 'First time client',
    },
  })

  await prisma.appointmentService.create({
    data: {
      appointmentId: appointment1.id,
      serviceId: services[0].id,
      price: 150,
      duration: 60,
    },
  })

  // Create loyalty programs
  await Promise.all([
    prisma.loyaltyProgram.create({
      data: {
        clientId: client1.id,
        points: 150,
        tier: 'Silver',
        totalSpent: 450,
      },
    }),
    prisma.loyaltyProgram.create({
      data: {
        clientId: client2.id,
        points: 80,
        tier: 'Bronze',
        totalSpent: 240,
      },
    }),
  ])

  console.log('✅ Database seeding completed successfully!')
  console.log('📊 Created:')
  console.log('  - 5 users (1 admin, 2 staff, 2 clients)')
  console.log('  - 2 locations')
  console.log(`  - ${realServiceData.length} real salon services`)
  console.log(`  - ${realServiceData.length * 2} location-service associations`)
  console.log('  - 1 sample appointment')
  console.log('  - 2 loyalty programs')

  // Count services by category
  const categoryCount = realServiceData.reduce((acc, service) => {
    acc[service.category] = (acc[service.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  console.log('📋 Services by category:')
  Object.entries(categoryCount).forEach(([category, count]) => {
    console.log(`  - ${category}: ${count} services`)
  })
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
