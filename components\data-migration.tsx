'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { StaffStorage } from '@/lib/staff-storage';
import { useToast } from '@/components/ui/use-toast';
import { Database, Upload, CheckCircle, AlertCircle } from 'lucide-react';

interface MigrationStatus {
  databaseStaffCount: number;
  databaseUserCount: number;
  migrationNeeded: boolean;
}

interface MigrationResult {
  success: boolean;
  migratedCount: number;
  migratedStaff: Array<{
    id: string;
    name: string;
    email: string;
    originalId: string;
  }>;
  errors: Array<{
    staff: string;
    error: string;
  }>;
  message: string;
}

export function DataMigration() {
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);
  const [localStorageStaffCount, setLocalStorageStaffCount] = useState(0);
  const { toast } = useToast();

  // Check migration status on component mount
  useEffect(() => {
    checkMigrationStatus();
    checkLocalStorageData();
  }, []);

  const checkMigrationStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/migrate-data');
      if (response.ok) {
        const status = await response.json();
        setMigrationStatus(status);
      }
    } catch (error) {
      console.error('Error checking migration status:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to check migration status'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkLocalStorageData = () => {
    try {
      const localStaff = StaffStorage.getStaff();
      setLocalStorageStaffCount(localStaff.length);
    } catch (error) {
      console.error('Error checking localStorage data:', error);
      setLocalStorageStaffCount(0);
    }
  };

  const migrateData = async () => {
    setIsMigrating(true);
    setMigrationResult(null);

    try {
      // Get localStorage data
      let localStaff = StaffStorage.getStaff();

      if (localStaff.length === 0) {
        toast({
          title: 'No Data to Migrate',
          description: 'No staff data found in localStorage'
        });
        return;
      }

      // First, clean up the data to remove duplicates
      const cleanupResponse = await fetch('/api/cleanup-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          staffData: localStaff
        })
      });

      if (cleanupResponse.ok) {
        const cleanupResult = await cleanupResponse.json();
        localStaff = cleanupResult.cleanedStaff;

        if (cleanupResult.duplicatesRemoved > 0) {
          toast({
            title: 'Data Cleaned',
            description: `Removed ${cleanupResult.duplicatesRemoved} duplicate/invalid entries before migration`
          });
        }
      }

      // Send cleaned data to migration endpoint
      const response = await fetch('/api/migrate-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          staffData: localStaff
        })
      });

      if (response.ok) {
        const result = await response.json();
        setMigrationResult(result);
        
        // Refresh migration status
        await checkMigrationStatus();
        
        toast({
          title: 'Migration Complete',
          description: result.message
        });
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Migration failed');
      }
    } catch (error) {
      console.error('Error during migration:', error);
      toast({
        variant: 'destructive',
        title: 'Migration Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsMigrating(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Migration
          </CardTitle>
          <CardDescription>
            Checking migration status...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={50} className="w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!migrationStatus) {
    return null;
  }

  // If migration is not needed and there's no localStorage data, don't show the component
  if (!migrationStatus.migrationNeeded && localStorageStaffCount === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Migration
        </CardTitle>
        <CardDescription>
          Migrate your localStorage data to the database for persistent storage
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Local Storage</h4>
            <p className="text-2xl font-bold">{localStorageStaffCount}</p>
            <p className="text-xs text-muted-foreground">Staff members</p>
          </div>
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Database</h4>
            <p className="text-2xl font-bold">{migrationStatus.databaseStaffCount}</p>
            <p className="text-xs text-muted-foreground">Staff members</p>
          </div>
        </div>

        {/* Migration Status */}
        {migrationStatus.migrationNeeded && localStorageStaffCount > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You have {localStorageStaffCount} staff members in localStorage that need to be migrated to the database for persistent storage.
            </AlertDescription>
          </Alert>
        )}

        {!migrationStatus.migrationNeeded && migrationStatus.databaseStaffCount > 0 && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Your data has been successfully migrated to the database. All changes will now persist across sessions.
            </AlertDescription>
          </Alert>
        )}

        {/* Migration Button */}
        {localStorageStaffCount > 0 && (
          <Button 
            onClick={migrateData} 
            disabled={isMigrating}
            className="w-full"
          >
            <Upload className="mr-2 h-4 w-4" />
            {isMigrating ? 'Migrating...' : `Migrate ${localStorageStaffCount} Staff Members`}
          </Button>
        )}

        {/* Migration Progress */}
        {isMigrating && (
          <div className="space-y-2">
            <Progress value={50} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              Migrating data to database...
            </p>
          </div>
        )}

        {/* Migration Results */}
        {migrationResult && (
          <div className="space-y-2">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Successfully migrated {migrationResult.migratedCount} staff members to the database.
              </AlertDescription>
            </Alert>
            
            {migrationResult.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {migrationResult.errors.length} errors occurred during migration:
                  <ul className="mt-2 list-disc list-inside">
                    {migrationResult.errors.map((error, index) => (
                      <li key={index} className="text-xs">
                        {error.staff}: {error.error}
                      </li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
